<svg width="136" height="92" viewBox="0 0 136 92" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M1.67113 86H5.89513L6.42313 78.272C6.51913 76.976 6.42313 75.392 6.42313 75.392H6.47113C6.47113 75.392 7.04713 77.192 7.43113 78.272L9.23113 83.24H12.8311L14.6311 78.272C15.0151 77.192 15.5911 75.392 15.5911 75.392H15.6391C15.6391 75.392 15.5431 76.976 15.6391 78.272L16.1671 86H20.3671L18.9751 68.84H14.4151L11.9191 76.088C11.5351 77.192 11.0551 78.92 11.0551 78.92H11.0071C11.0071 78.92 10.5271 77.192 10.1431 76.088L7.64713 68.84H3.08713L1.67113 86ZM24.5988 86H35.6868V82.4H28.7988V79.16H34.0308V75.56H28.7988V72.44H35.3508V68.84H24.5988V86ZM42.7162 86H46.9162V72.44H52.1242V68.84H37.5082V72.44H42.7162V86ZM54.9607 86H59.1607V80.216H61.1287L64.1767 86H68.8567L65.5207 80C65.1127 79.28 64.8727 78.968 64.8727 78.968V78.92C66.5767 78.056 67.4407 76.064 67.4407 74.24C67.4407 71.744 66.2167 69.992 64.2727 69.248C63.5287 68.96 62.6887 68.84 60.9367 68.84H54.9607V86ZM59.1607 76.616V72.44H60.5767C62.6887 72.44 63.1687 73.208 63.1687 74.504C63.1687 75.872 62.3287 76.616 60.9847 76.616H59.1607ZM70.8172 77.312C70.8172 82.352 74.6092 86.288 79.7932 86.288C84.9772 86.288 88.7692 82.352 88.7692 77.312C88.7692 72.392 84.9772 68.552 79.7932 68.552C74.6092 68.552 70.8172 72.392 70.8172 77.312ZM75.1612 77.312C75.1612 74.504 77.2252 72.392 79.7932 72.392C82.3612 72.392 84.4252 74.504 84.4252 77.312C84.4252 80.24 82.3612 82.448 79.7932 82.448C77.2252 82.448 75.1612 80.24 75.1612 77.312ZM92.3538 86H96.5538V78.008C96.5538 76.928 96.3618 75.08 96.3618 75.08H96.4098C96.4098 75.08 97.1778 76.88 97.8498 78.008L102.626 86H106.85V68.84H102.65V76.856C102.65 77.936 102.842 79.784 102.842 79.784H102.794C102.794 79.784 102.026 77.984 101.354 76.856L96.6018 68.84H92.3538V86ZM111.613 86H115.813V68.84H111.613V86ZM119.383 77.384C119.383 82.472 123.007 86.288 128.383 86.288C132.871 86.288 135.295 83.408 135.295 83.408L133.135 80.336C133.135 80.336 131.167 82.448 128.575 82.448C125.407 82.448 123.727 79.832 123.727 77.288C123.727 74.816 125.287 72.392 128.575 72.392C130.951 72.392 132.871 74.144 132.871 74.144L134.815 70.976C134.815 70.976 132.631 68.552 128.383 68.552C123.223 68.552 119.383 72.392 119.383 77.384Z" fill="white"/>
<g filter="url(#filter0_i)">
<path d="M90.1139 2.77686L105.322 50.1304C106.152 52.7123 104.226 55.3535 101.514 55.3535H90.3306C88.5878 55.3535 87.0456 54.2251 86.5182 52.5641L71.4826 5.21052C70.6636 2.63121 72.5888 0 75.295 0H86.3055C88.0434 0 89.5825 1.12219 90.1139 2.77686Z" fill="#F9666E"/>
<path d="M90.1139 2.77686L105.322 50.1304C106.152 52.7123 104.226 55.3535 101.514 55.3535H90.3306C88.5878 55.3535 87.0456 54.2251 86.5182 52.5641L71.4826 5.21052C70.6636 2.63121 72.5888 0 75.295 0H86.3055C88.0434 0 89.5825 1.12219 90.1139 2.77686Z" stroke="#756A6A"/>
</g>
<g filter="url(#filter1_i)">
<path d="M75.5486 37.8L64.8244 3.44919C64.1838 1.39729 62.2838 0 60.1342 0C57.5178 0 55.3603 2.05036 55.2272 4.66341L53.9188 30.3392C53.8724 31.2511 53.9824 32.1643 54.2441 33.0391L60.0657 52.4999C60.5723 54.1934 62.1303 55.3535 63.8979 55.3535H69.3094C71.0505 55.3535 72.5915 54.2274 73.1204 52.5686L75.5268 45.0214C76.2753 42.6736 76.283 40.1523 75.5486 37.8Z" fill="white"/>
<path d="M75.5486 37.8L64.8244 3.44919C64.1838 1.39729 62.2838 0 60.1342 0C57.5178 0 55.3603 2.05036 55.2272 4.66341L53.9188 30.3392C53.8724 31.2511 53.9824 32.1643 54.2441 33.0391L60.0657 52.4999C60.5723 54.1934 62.1303 55.3535 63.8979 55.3535H69.3094C71.0505 55.3535 72.5915 54.2274 73.1204 52.5686L75.5268 45.0214C76.2753 42.6736 76.283 40.1523 75.5486 37.8Z" fill="url(#paint0_linear)"/>
</g>
<g filter="url(#filter2_i)">
<path d="M49.317 0H60.3316C63.0288 0 64.9529 2.61489 64.1505 5.18995L49.2695 52.9475C48.7487 54.619 47.2014 55.7576 45.4506 55.7576H34.4361C31.7389 55.7576 29.8148 53.1427 30.6172 50.5676L45.4981 2.81005C46.019 1.13856 47.5663 0 49.317 0Z" fill="#F9666E"/>
<path d="M49.317 0H60.3316C63.0288 0 64.9529 2.61489 64.1505 5.18995L49.2695 52.9475C48.7487 54.619 47.2014 55.7576 45.4506 55.7576H34.4361C31.7389 55.7576 29.8148 53.1427 30.6172 50.5676L45.4981 2.81005C46.019 1.13856 47.5663 0 49.317 0Z" stroke="black"/>
</g>
<defs>
<filter id="filter0_i" x="71.292" y="0" width="34.2246" height="55.3535" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.886275 0 0 0 0 0.337255 0 0 0 0 0.364706 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter1_i" x="53.9082" y="0" width="22.1855" height="55.3535" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-4" dy="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.904167 0 0 0 0 0.892865 0 0 0 0 0.892865 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<filter id="filter2_i" x="30.4336" y="0" width="33.9004" height="55.7576" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="4" dy="5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8875 0 0 0 0 0.33651 0 0 0 0 0.366496 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow"/>
</filter>
<linearGradient id="paint0_linear" x1="58.0002" y1="24.5" x2="69.5002" y2="27.5" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0.25"/>
<stop offset="0.911458" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
