</div>
<!--end::Content-->
</div>
<!--end::Wrapper-->
</div>
<!--end::Page-->
</div>
<!--end::Root-->
<!--end::Main-->
<!--begin::Javascript-->
<?php

use <PERSON>z\Admin\Settings;
use Wayz\Controllers\Statistics;
use Wayz\Utils;

wp_footer();
if ( is_page_template( 'templates/page-add-contract.php' ) ) {
	?>
	<div class="modal fade" tabindex="-1" id="add_trip_modal">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h3 class="modal-title">Add Trip</h3>

					<!--begin::Close-->
					<div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
						<span class="svg-icon svg-icon-1"></span>
					</div>
					<!--end::Close-->
				</div>

				<div class="modal-body">
					<!--begin::Input group-->
					<form class="fv-row mb-10" id="add_trip_form">
						<div class="mb-4 fv-row">
							<label class="form-label fs-6 text-gray-700 required" for="company_country">Origin country</label>
							<select required name="trip_origin_country" id="trip_origin_country" class="form-select form-select-solid" data-control="select2" data-dropdown-parent="#add_trip_modal" data-placeholder="Select an option">
								<option value="" selected>Select a Country...</option>
								<?php
								$countries = wayz_get_countries();
								foreach ( $countries as $country ) {
									echo '<option value="' . $country['country'] . '">' . $country['country'] . '</option>';
								}
								?>
							</select>
						</div>
						<div class="mb-4 fv-row">
							<label class="form-label fs-6 text-gray-700 required" for="company_country">Destination country</label>
							<select required name="trip_destination_country" id="trip_destination_country" class="form-select form-select-solid" data-control="select2" data-dropdown-parent="#add_trip_modal" data-placeholder="Select an option">
								<option value="" selected>Select a Country...</option>
								<?php
								foreach ( $countries as $country ) {
									echo '<option value="' . $country['country'] . '">' . $country['country'] . '</option>';
								}
								?>
							</select>
						</div>
						<div class="form-label fs-6 text-gray-700 d-none" id="trip_cost_data">
							<!--begin::Label-->
							<label class="form-label required">Cost</label>
							<!--end::Label-->

							<!--begin::Input-->
							<input type="number" required class="form-control form-control-solid" id="trip_cost" name="trip_cost" placeholder="" value=""/>
							<!--end::Input-->
						</div>
						<div class="mt-3">
							<button id="add_trip" type="submit" class="btn btn-primary"> Add trip</button>
							<button id="add_trip_close" type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
						</div>
					</form>
					<!--end::Input group-->
				</div>
			</div>
		</div>
	</div>
	<script>
        $("#contract_end_date").flatpickr({
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            allowInput: true
        });

        /*
        Form validator
         */
        jQuery(function () {
            // Initialize form validation on the registration form.
            // It has the name attribute "registration"
            jQuery("#add_trip_form").validate({
                // Specify validation rules
                rules: {
                    // The key name on the left side is the name attribute
                    // of an input field. Validation rules are defined
                    // on the right side
                    trip_origin_country: "required",
                    trip_destination_country: "required"
                },
                // Specify validation error messages
                messages: {
                    trip_origin_country: "Please enter origin country",
                    trip_destination_country: "Please enter destination country"
                },
                // Make sure the form is submitted to the destination defined
                // in the "action" attribute of the form when valid
                submitHandler: function (form) {
                    jQuery('#trips_table').find('tbody').append("<tr><td>" + jQuery("#trip_origin_country").val() + "</td>" +
                        "<td>" + jQuery("#trip_destination_country").val() + "</td>" +
                        "<td class='trip_cost'>" + jQuery("#trip_cost").val() + "</td>" +
                        '<td class="text-end"><button class="btn btn-hover-scale p-0 remove-trip" type="button"><i class="bi bi-x-circle fs-2x text-danger"></i></button>' +
                        "<input name='contract_trips[]' type='hidden' hidden value='" + jQuery("#trip_origin_country").val() + "||" + jQuery("#trip_destination_country").val() + "||" + jQuery("#trip_cost").val() + "'>" +
                        "</td>" +
                        "</tr>");
                    jQuery("#trip_origin_country").select2("val", "default");
                    jQuery("#trip_destination_country").select2("val", "default");
                    jQuery("#trips_validator").val("valid");
                    form.reset();
                    jQuery('#add_trip_close').click();
                }
            });
        });

        var element = document.querySelector("#add_contract_stepper");
        var submit = document.querySelector('[data-kt-stepper-action="submit"]');
        var next = document.querySelector('[data-kt-stepper-action="next"]');
        var previous = document.querySelector('[data-kt-stepper-action="previous"]');

        // Initialize Stepper
        var stepper = new KTStepper(element);
        // Handle next step
        /*jQuery('[data-kt-stepper-action="next"]').on('click', function() {
            console.log(jQuery("#add_contract_form").valid());
        });*/

        stepper.on("kt.stepper.next", function (stepper) {
            var currentStep;
            currentStep = stepper.getCurrentStepIndex();

            if (validate_fields(currentStep)) {
                stepper.goNext();
            }
            //stepper.goNext(); // go next step
            // Hide next button and show submit
            display_hide_buttons(stepper);
        });

        // Handle previous step
        stepper.on("kt.stepper.previous", function (stepper) {
            stepper.goPrevious(); // go previous step
            display_hide_buttons(stepper);
        });

        function display_hide_buttons(stepper) {
            if (3 === stepper.getCurrentStepIndex()) {
                submit.classList.remove("d-none");
                submit.classList.add("d-inline-block");

                next.classList.remove("d-inline-block");
                next.classList.add("d-none");
            } else if (4 === stepper.getCurrentStepIndex()) {
                submit.classList.remove("d-inline-block");
                submit.classList.add("d-none");

                next.classList.remove("d-inline-block");
                next.classList.add("d-none");

                previous.classList.remove("d-inline-block");
                previous.classList.add("d-none");
            } else if (2 === stepper.getCurrentStepIndex() || 1 === stepper.getCurrentStepIndex()) {
                submit.classList.remove("d-inline-block");
                submit.classList.add("d-none");

                next.classList.remove("d-none");
                next.classList.add("d-inline-block");
            }
        }

        function validate_fields(currentStep) {
            let validator = jQuery("#add_contract_form").validate();

            if (1 === currentStep)
                return (validator.element('#contract_title') && validator.element('#contract_end_date') && validator.element('input[name="contract_type"]') && validator.element('#fright_companies'));

            if (2 === currentStep) {
                if (jQuery("#money_contract").is(":checked"))
                    return (validator.element('#contract_maximum_money'));
                else
                    return (validator.element('#contract_maximum_containers'));
            }

            if (3 === currentStep) {
                if (validator.element('#trips_validator')) {
                    return true;
                }
            }

            return false;
        }

        /*
        Contract type change
         */
        document.getElementById('money_contract').addEventListener('change', (event) => {
            if (event.currentTarget.checked) {
                displayMoney();
            }
        });

        document.getElementById('container_contract').addEventListener('change', (event) => {
            if (event.currentTarget.checked) {
                displayContainers();
            }
        });

        function displayContainers() {
            document.getElementById('money_data').classList.add("d-none");
            document.getElementById('trip_cost_data').classList.add("d-none");
            document.getElementById('contract_edit_cost').classList.add("d-none");
            document.getElementById('containers_data').classList.remove("d-none");
            jQuery(".trip_cost").addClass("d-none");
        }

        function displayMoney() {

            document.getElementById('money_data').classList.remove("d-none");
            document.getElementById('trip_cost_data').classList.remove("d-none");
            document.getElementById('contract_edit_cost').classList.remove("d-none");
            document.getElementById('containers_data').classList.add("d-none");
            jQuery(".trip_cost").removeClass("d-none");
        }

        $(document).ready(function () {
            if (document.getElementById('money_contract').checked) {
                displayMoney();
            } else if (document.getElementById('container_contract').checked) {
                displayContainers();
            }
        });

        jQuery('#trips_table_body').on('click', '.remove-trip', function () {
            // Removing the current row.
            jQuery(this).closest('tr').remove();
        });
	</script>
	<?php
}
if ( is_page_template( 'templates/page-quotations-reports.php' ) || is_page_template( 'templates/page-orders-reports.php' ) || is_page_template( 'templates/page-offers-reports.php' ) ) {
	?>
	<script>
        $('#reportExport').click(function () {
            $("#kt_ecommerce_sales_table").table2excel({
                name: "Report",
                filename: "report.xls",
                preserveColors: false
            });
        });
	</script>
	<?php
}
?>
<script>
	<?php
	if ( is_singular( 'quotations' ) ) {
	$decline_link = add_query_arg( array( 'decline' => 'decline' ) );
	?>
    $(document).ready(function () {
        const decline_link = '<?php echo $decline_link; ?>';
        $('#declineLink').attr('href', decline_link);
    });
	<?php
	$sp_fields = Utils::get_shipment_data_fields();
	foreach ( $sp_fields as $shipment_data_group ) {
		foreach ( $shipment_data_group['fields'] as $key => $field ) {
			if ( 'date' === $field['type'] ) {
				?>
	$("#<?php echo $key;?>").flatpickr({
		enableTime: true,
		dateFormat: "Y-m-d H:i",
		allowInput: true
	});
	<?php
			}
		}
	}
	}
	?>

    /*
    Cities and ports
     */
	<?php
	if(is_page_template( 'templates/page-get-quotations.php' ) || is_page_template( 'templates/page-add-contract.php' ) || is_page_template( 'templates/page-add-company.php' )){
	?>
    jQuery(document).ready(function () {
        refresh_ports(jQuery("#origin_country").val(), 'origin_port');
        refresh_ports(jQuery("#destination_country").val(), 'destination_port');
        refresh_ports(jQuery("#company_country").val(), 'company_port');

		$('input[name="shipment_type"]').change(function() {
			jQuery("#destination_port").select2("val", "default");
			jQuery("#origin_country").select2("val", "default");
			jQuery("#origin_port").select2("val", "default");
			jQuery("#destination_country").select2("val", "default");
		});

        jQuery("#origin_country").change(function (e) {
            refresh_ports(jQuery("#origin_country").val(), 'origin_port');
        });

        jQuery("#destination_country").change(function (e) {
            refresh_ports(jQuery("#destination_country").val(), 'destination_port');
        });

        jQuery("#company_country").change(function (e) {
            refresh_ports(jQuery("#company_country").val(), 'company_port');
        });

        /*
        Add new address target
         */
        jQuery("#pickup_address_button").click(function (e) {
            jQuery('#add_address_target').val('pickup_address');
        });

        jQuery("#destination_location_button").click(function (e) {
            jQuery('#add_address_target').val('destination_location');
        });
    });

    function refresh_ports(value, target_id) {
		var port_type = $('input[name="shipment_type"]:checked').val();

        var data = {
            'action': 'wayz_get_country_ports',
            'country': value,
			'port_type' : port_type,
        };
        jQuery.post(wayz_object.ajax_url, data, function (response) {
            document.getElementById(target_id).innerHTML = response;
        });
    }
	<?php
	}
	?>
</script>
<?php
if ( is_page_template( 'templates/page-dashboard.php' ) ) {
	?>
	<script>
        const ctx = document.getElementById('saving_chart').getContext('2d');
        const myChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
                datasets: [{
                    label: '# Savings',
                    data: <?php echo ( new Statistics() )->get_data( get_current_user_id() );?>,
                    backgroundColor: 'rgb(255, 99, 132)',
                    borderColor: 'rgb(255, 99, 132)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
	</script>
	<?php
}

if ( is_page_template( 'templates/page-get-quotations.php' ) || is_page_template( 'templates/page-add-contract.php' ) ) {
	$addCompanyPage = get_pages( array(
		'meta_key'   => '_wp_page_template',
		'meta_value' => 'templates/page-add-company.php'
	) );
	if ( isset( $addCompanyPage[0] ) ) {
		$addCompanyPageLink = get_permalink( $addCompanyPage[0]->ID );
	} else {
		$addCompanyPageLink = '#';
	}
	?>
	<!--begin::Modal - New Company-->
	<div class="modal fade" id="kt_modal_new_company" tabindex="-1" aria-hidden="true">
		<!--begin::Modal dialog-->
		<div class="modal-dialog modal-dialog-centered mw-650px">
			<!--begin::Modal content-->
			<div class="modal-content">
				<!--begin::Form-->
				<form class="form" method="post" action="<?php echo $addCompanyPageLink; ?>" id="kt_modal_new_company_form">
					<input hidden style="display:none;" name="add_new_company" value="add_new_company" type="hidden">
					<input hidden style="display:none;" name="action" value="wayz_add_provider" type="hidden">
					<!--begin::Modal header-->
					<div class="modal-header" id="kt_modal_new_company_header">
						<!--begin::Modal title-->
						<h2>Add New Company</h2>
						<!--end::Modal title-->
						<!--begin::Close-->
						<div class="btn btn-sm btn-icon btn-active-color-primary"
						     data-bs-dismiss="modal">
							<!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
							<span class="svg-icon svg-icon-1">
								<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
									<rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor"/>
									<rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor"/>
								</svg>
							</span>
							<!--end::Svg Icon-->
						</div>
						<!--end::Close-->
					</div>
					<!--end::Modal header-->
					<!--begin::Modal body-->
					<div class="modal-body py-10 px-lg-17">
						<!--begin::Scroll-->
						<div class="scroll-y me-n7 pe-7" id="kt_modal_new_company_scroll" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_new_company_header" data-kt-scroll-wrappers="#kt_modal_new_company_scroll" data-kt-scroll-offset="300px">
							<div class="row">
								<div class="col-xl-6">
									<label class="form-label fs-3 fw-bolder text-gray-900 mb-6">Company Form</label>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required" for="company_name">Company</label>
										<input type="text" class="form-control form-control-solid" placeholder="" id="company_name" name="company_name" required/>
									</div>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required" for="company_location">Location</label>
										<input id="company_location" name="location" type="text" class="form-control form-control-solid" placeholder="" required>
									</div>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required" for="company_country">Country</label>
										<select required name="company_country" id="company_country" class="form-select form-select-solid" data-control="select2" data-dropdown-parent="#kt_modal_new_company_form" data-placeholder="Select an option">
											<option value="" selected>Select a Country...</option>
											<?php
											$countries = wayz_get_countries();
											foreach ( $countries as $country ) {
												echo '<option value="' . $country['country'] . '">' . $country['country'] . '</option>';
											}
											?>
										</select>
									</div>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required">City</label>
										<input name="city" type="text" class="form-control form-control-solid" placeholder="" required>
									</div>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required">Port</label>
										<select required id="company_port" name="company_port" class="form-select" data-control="select2" data-dropdown-parent="#kt_modal_new_company_form" data-placeholder="Select an option">
											<option>-</option>
										</select>
									</div>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required">Address 1</label>
										<input name="address1" id="address1" type="text" class="form-control form-control-solid" placeholder="" required>
									</div>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700">Address 2</label>
										<input name="address2" type="text" class="form-control form-control-solid" placeholder="">
									</div>
								</div>

								<div class="col-xl-6">
									<label class="form-label fs-3 fw-bolder text-gray-900 mb-6">Details Option</label>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required">Name</label>
										<input name="contact" type="text" class="form-control form-control-solid" placeholder="" required>
									</div>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required">Phone Number</label>
										<input name="phone" type="number" class="form-control form-control-solid" placeholder="" required>
									</div>
									<div class="mb-4 fv-row">
										<label class="form-label fs-6 fw-bolder text-gray-700 required">Email</label>
										<input name="email" type="email" class="form-control form-control-solid" placeholder="" required>
									</div>
								</div>
							</div>
						</div>
						<!--end::Scroll-->
					</div>
					<!--end::Modal body-->
					<!--begin::Modal footer-->
					<div class="modal-footer flex-center">
						<!--begin::Button-->
						<button type="reset" id="kt_modal_new_company_cancel" class="btn btn-light me-3">Discard</button>
						<!--end::Button-->
						<!--begin::Button-->
						<button type="submit" id="kt_modal_new_company_submit" class="btn btn-primary">
							<span class="indicator-label">Add</span>
							<span class="indicator-progress">Please wait...<span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
						</button>
						<!--end::Button-->
					</div>
					<!--end::Modal footer-->
				</form>
				<!--end::Form-->
			</div>
		</div>
	</div>
	<!--End::Modal - New Company-->
	<?php
}

if ( is_page_template( 'templates/page-get-quotations.php' ) ) {
    ?>
    <script>
        $("#pick_up_date").flatpickr({
            minDate: "today",
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            allowInput: true
        });

        $("#deliver_by_date").flatpickr({
            minDate: "today",
            enableTime: true,
            dateFormat: "Y-m-d",
            allowInput: true
        });
    </script>
    <?php
}
?>
<!--end::Page Custom Javascript-->
<!--end::Javascript-->
<style>
    #google_map{
        width: 100%;
        height: 480px;
    }
</style>
<script>
	jQuery(document).ready(function() {
		jQuery('#containersTable tr:first-child button[data-kt-element="remove-item"]').hide();
		jQuery('#airFrightFieldsTable tr:first-child button[data-kt-element="remove-item"]').hide();

		jQuery('#quotationForm_submit_button').on('click', function(event) {
			scroll_to_empty_field();
		});

		jQuery('#save_draft').on('click', function(event) {
			scroll_to_empty_field();
		});

		function scroll_to_empty_field() {
			var currentForm = jQuery('#kt_invoice_form');
			var requiredFields = currentForm.find('input[required], select[required], textarea[required]');
			var firstEmptyField = null;

			requiredFields.each(function() {
				if (!jQuery(this).val()) {
					event.preventDefault(); // Prevent form submission

					if (!firstEmptyField) {
						firstEmptyField = this;
					}
				}
			});

			if (firstEmptyField) {
				var headerHeight = 160; // Replace with the actual height of your fixed header
				var scrollToPosition = jQuery(firstEmptyField).offset().top - headerHeight;

				jQuery('html, body').animate({
					scrollTop: scrollToPosition
				}, 500); // Scroll to the first empty required field

				jQuery(firstEmptyField).focus(); // Set focus to the field
				jQuery(firstEmptyField).addClass('is-invalid  border-danger');
			}
		}

		$('input').on('input', function() {
			// Remove the is-invalid and border-danger classes if the input value is not empty
			if ($(this).val().trim() !== '') {
				$(this).removeClass('is-invalid border-danger');
			}
		});
	});

</script>

<script>
    document.getElementById('destination_google_map_selector').style.display = "none";
    var destination_location = document.getElementById('destination_location');
    destination_location.onchange = (event) => {
        if(event.target.value === 'map_selector'){
            document.getElementById('destination_google_map_selector').style.display = "block";
        } else {
            document.getElementById('destination_google_map_selector').style.display = "none";
        }
    }

    var destination_location_settings = {
        setCurrentPosition: true,
    };
    if( destination_location.value === 'map_selector' ) {
        document.getElementById('destination_google_map_selector').style.display = "block";
	    destination_location_settings = {
            setCurrentPosition: false,
            lat: document.getElementById('destination_location_lat').value,
            lng: document.getElementById('destination_location_lng').value
        };
    }

    document.getElementById('pickup_google_map_selector').style.display = "none";
    var pickup_address = document.getElementById('pickup_address');
    pickup_address.onchange = (event) => {
        if(event.target.value === 'map_selector'){
            document.getElementById('pickup_google_map_selector').style.display = "block";
        } else {
            document.getElementById('pickup_google_map_selector').style.display = "none";
        }
    }

    var pickup_address_settings = {
        setCurrentPosition: true,
    };
    if( pickup_address.value === 'map_selector' ) {
        document.getElementById('pickup_google_map_selector').style.display = "block";

	    pickup_address_settings = {
            setCurrentPosition: false,
            lat: document.getElementById('pickup_location_lat').value,
            lng: document.getElementById('pickup_location_lng').value
        };

        console.log(pickup_address_settings);
    }

    // Initialize locationPicker plugin
    var destination_lp = new locationPicker('destination_google_map', destination_location_settings, {
        zoom: 15 // You can set any google map options here, zoom defaults to 15
    });

    var pickup_lp = new locationPicker('pickup_google_map', pickup_address_settings, {
        zoom: 15 // You can set any google map options here, zoom defaults to 15
    });
    // Listen to map idle event, listening to idle event more accurate than listening to ondrag event
    google.maps.event.addListener(destination_lp.map, 'idle', function (event) {
        // Get current location and show it in HTML
        var location = destination_lp.getMarkerPosition();
        document.getElementById('destination_location_lat').value = location.lat;
        document.getElementById('destination_location_lng').value = location.lng;
    });

    google.maps.event.addListener(pickup_lp.map, 'idle', function (event) {
        // Get current location and show it in HTML
        var location = pickup_lp.getMarkerPosition();
        document.getElementById('pickup_location_lat').value = location.lat;
        document.getElementById('pickup_location_lng').value = location.lng;
    });
</script>

<?php
if ( Settings::get_setting( 'air_fight_feature' ) ) {
	?>
	<script>
        // Get all radio buttons with name "shipment_type"
        var radioButtons = document.querySelectorAll('input[name="shipment_type"]');

        // Function to show/hide fields based on the selected value
        function toggleFields() {
            var selectedValue = document.querySelector('input[name="shipment_type"]:checked').value;

            if (selectedValue === 'air') {
                document.getElementById('airFrightFieldsTable').style.display = "block";
                document.getElementById('containersTable').style.display = "none";

                $('#containersTable input').each(function(i) {
                    $(this).removeAttr('required');
                });

                $('#airFrightFieldsTable input').each(function(i) {
                    $(this).attr('required', "required");
                });
            } else {
                document.getElementById('airFrightFieldsTable').style.display = "none";
                document.getElementById('containersTable').style.display = "block";

                $('#containersTable input').each(function(i) {
                    $(this).attr('required', "required");
                });

                $('#airFrightFieldsTable input').each(function(i) {
                    $(this).removeAttr('required');
                });
            }
        }

        // Add event listener to each radio button
        radioButtons.forEach(function(radio) {
            radio.addEventListener('change', toggleFields);
        });

        // Check the value on page load and toggle the fields
        toggleFields();
	</script>
	<?php
}
?>
</body>
<!--end::Body-->

</html>