{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "3c8cb209757ffb9ad9372602ea34429c", "packages": [{"name": "box/spout", "version": "v3.3.0", "source": {"type": "git", "url": "https://github.com/box/spout.git", "reference": "9bdb027d312b732515b884a341c0ad70372c6295"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/box/spout/zipball/9bdb027d312b732515b884a341c0ad70372c6295", "reference": "9bdb027d312b732515b884a341c0ad70372c6295", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlreader": "*", "ext-zip": "*", "php": ">=7.2.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2", "phpunit/phpunit": "^8"}, "suggest": {"ext-iconv": "To handle non UTF-8 CSV files (if \"php-intl\" is not already installed or is too limited)", "ext-intl": "To handle non UTF-8 CSV files (if \"iconv\" is not already installed)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Box\\Spout\\": "src/Spout"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP Library to read and write spreadsheet files (CSV, XLSX and ODS), in a fast and scalable way", "homepage": "https://www.github.com/box/spout", "keywords": ["OOXML", "csv", "excel", "memory", "odf", "ods", "office", "open", "php", "read", "scale", "spreadsheet", "stream", "write", "xlsx"], "support": {"issues": "https://github.com/box/spout/issues", "source": "https://github.com/box/spout/tree/v3.3.0"}, "abandoned": true, "time": "2021-05-14T21:18:09+00:00"}, {"name": "malkusch/lock", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/php-lock/lock.git", "reference": "66ccdccbb3b9a2cabdeba077f6aa1759dd3fbcd2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-lock/lock/zipball/66ccdccbb3b9a2cabdeba077f6aa1759dd3fbcd2", "reference": "66ccdccbb3b9a2cabdeba077f6aa1759dd3fbcd2", "shasum": ""}, "require": {"php": ">=7.4 <8.4", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"eloquent/liberator": "^2.0 || ^3.0", "ergebnis/composer-normalize": "^2.13", "ergebnis/phpunit-slow-test-detector": "^2.9", "ext-memcached": "*", "ext-pcntl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-pdo_sqlite": "*", "ext-sysvsem": "*", "friendsofphp/php-cs-fixer": "^3.0", "mikey179/vfsstream": "^1.6.11", "php-mock/php-mock-phpunit": "^2.1", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^2.0", "phpstan/phpstan-deprecation-rules": "^2.0", "phpstan/phpstan-strict-rules": "^2.0", "phpunit/phpunit": "^9.5.25 || ^10.0 || ^11.0", "predis/predis": "^1.1.8", "spatie/async": "^1.5"}, "suggest": {"ext-igbinary": "To use this library with PHP Redis igbinary serializer enabled.", "ext-lzf": "To use this library with PHP Redis lzf compression enabled.", "ext-pnctl": "Enables locking with flock without busy waiting in CLI scripts.", "ext-redis": "To use this library with the PHP Redis extension.", "ext-sysvsem": "Enables locking using semaphores.", "predis/predis": "To use this library with predis."}, "type": "library", "autoload": {"psr-4": {"malkusch\\lock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://markus.malkusch.de"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "homepage": "https://mvorisek.cz/"}], "description": "Mutex library for exclusive code execution.", "homepage": "https://github.com/malkusch/lock", "keywords": ["advisory-locks", "cas", "flock", "lock", "locking", "memcache", "mutex", "mysql", "postgresql", "redis", "redlock", "semaphore"], "support": {"issues": "https://github.com/php-lock/lock/issues", "source": "https://github.com/php-lock/lock/tree/2.3.0"}, "time": "2024-12-05T22:54:07+00:00"}, {"name": "psr/log", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "reference": "f16e1d5863e37f8d8c2a01719f5b34baa2b714d3", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.2"}, "time": "2024-09-11T13:17:53+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/6d857f4d76bd4b343eac26d6b539585d2bc56493", "reference": "6d857f4d76bd4b343eac26d6b539585d2bc56493", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=7.2"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-12-23T08:48:59+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.32.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "reference": "0cc9dd0f17f61d8131e7df6b84bd344899fe2608", "shasum": ""}, "require": {"php": ">=7.2"}, "type": "library", "extra": {"thanks": {"url": "https://github.com/symfony/polyfill", "name": "symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.32.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2025-01-02T08:10:11+00:00"}, {"name": "symfony/var-dumper", "version": "v5.4.48", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/42f18f170aa86d612c3559cfb3bd11a375df32c8", "reference": "42f18f170aa86d612c3559cfb3bd11a375df32c8", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/console": "<4.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/uid": "^5.1|^6.0", "twig/twig": "^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v5.4.48"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-11-08T15:21:10+00:00"}, {"name": "tightenco/collect", "version": "v7.26.1", "source": {"type": "git", "url": "https://github.com/tighten/collect.git", "reference": "5e460929279ad806e59fc731e649e9b25fc8774a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tighten/collect/zipball/5e460929279ad806e59fc731e649e9b25fc8774a", "reference": "5e460929279ad806e59fc731e649e9b25fc8774a", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/var-dumper": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"mockery/mockery": "^1.0", "nesbot/carbon": "^2.23.0", "phpunit/phpunit": "^7.0"}, "type": "library", "autoload": {"files": ["src/Collect/Support/helpers.php", "src/Collect/Support/alias.php"], "psr-4": {"Tightenco\\Collect\\": "src/Collect"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Collect - Illuminate Collections as a separate package.", "keywords": ["collection", "laravel"], "support": {"issues": "https://github.com/tighten/collect/issues", "source": "https://github.com/tighten/collect/tree/v7.26.1"}, "abandoned": "illuminate/collections", "time": "2020-09-05T00:05:48+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "plugin-api-version": "2.6.0"}