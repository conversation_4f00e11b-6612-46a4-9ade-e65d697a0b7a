<?php

use Wayz\Notifications\Notification;

?>
<!DOCTYPE html>
<html lang="en">
<head>
	<base href="">
	<title></title>
	<meta charset="utf-8"/>
	<meta name="viewport" content="width=device-width, initial-scale=1"/>
	<meta property="og:locale" content="en_US"/>
    <?php wp_head();
    $user                   = wp_get_current_user();
    $is_shipping_company    = ( in_array( 'shipping_company', $user->roles ) && ! in_array( 'administrator', $user->roles ) );
    $offers_notifications   = ( new Notification() )->get_user_notifications( get_current_user_id(), 'new_offer' );
    $orders_notifications   = ( new Notification() )->get_user_notifications( get_current_user_id(), 'order_status' );
    $shipment_notifications = ( new Notification() )->get_user_notifications( get_current_user_id(), 'shipment_status' );
    ?>
</head>
<!--end::Head-->
<!--begin::Body-->

<body id="kt_body"
      class="<?php if ( ! $is_shipping_company ) echo 'header-fixed toolbar-fixed aside-enabled aside-fixed ';?> header-tablet-and-mobile-fixed toolbar-enabled"
      style="--kt-toolbar-height:55px;--kt-toolbar-height-tablet-and-mobile:55px">
<!--begin::Main-->
<!--begin::Root-->
<div class="d-flex flex-column flex-root">
	<!--begin::Page-->
	<div class="page d-flex flex-row flex-column-fluid">
        <?php
        if ( ! $is_shipping_company ) {
            ?>
            <!--begin::Aside-->
            <div id="kt_aside" class="aside aside-dark aside-hoverable" data-kt-drawer="true"
                 data-kt-drawer-name="aside" data-kt-drawer-activate="{default: true, lg: false}"
                 data-kt-drawer-overlay="true" data-kt-drawer-width="{default:'200px', '300px': '250px'}"
                 data-kt-drawer-direction="start" data-kt-drawer-toggle="#kt_aside_mobile_toggle">
                <!--begin::Brand-->
                <div class="aside-logo flex-column-auto" id="kt_aside_logo">
                    <!--begin::Logo-->
                    <div class="menu-item me-lg-1">0
                        <a href="<?php echo home_url();?>">
                            <img alt="logo" src="<?php echo get_template_directory_uri().'/'; ?>assets/images/logo_wayz.png" class="logo logo-white h-25px h-md-35px">
                        </a>
                    </div>
                    <!--end::Logo-->
                    <!--begin::Aside toggler-->
                    <div id="kt_aside_toggle" class="btn btn-icon w-auto px-0 btn-active-color-primary aside-toggle" data-kt-toggle="true" data-kt-toggle-state="active" data-kt-toggle-target="body" data-kt-toggle-name="aside-minimize">
                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr079.svg-->
                        <span class="svg-icon svg-icon-1 rotate-180">
								<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
									<path opacity="0.5" d="M14.2657 11.4343L18.45 7.25C18.8642 6.83579 18.8642 6.16421 18.45 5.75C18.0358 5.33579 17.3642 5.33579 16.95 5.75L11.4071 11.2929C11.0166 11.6834 11.0166 12.3166 11.4071 12.7071L16.95 18.25C17.3642 18.6642 18.0358 18.6642 18.45 18.25C18.8642 17.8358 18.8642 17.1642 18.45 16.75L14.2657 12.5657C13.9533 12.2533 13.9533 11.7467 14.2657 11.4343Z" fill="currentColor" />
									<path d="M8.2657 11.4343L12.45 7.25C12.8642 6.83579 12.8642 6.16421 12.45 5.75C12.0358 5.33579 11.3642 5.33579 10.95 5.75L5.40712 11.2929C5.01659 11.6834 5.01659 12.3166 5.40712 12.7071L10.95 18.25C11.3642 18.6642 12.0358 18.6642 12.45 18.25C12.8642 17.8358 12.8642 17.1642 12.45 16.75L8.2657 12.5657C7.95328 12.2533 7.95328 11.7467 8.2657 11.4343Z" fill="currentColor" />
								</svg>
							</span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Aside toggler-->
                </div>
                <!--end::Brand-->
                <!--begin::Aside menu-->
                <div class="aside-menu flex-column-fluid">
                    <!--begin::Aside Menu-->
                    <div class="hover-scroll-overlay-y my-5 my-lg-5" id="kt_aside_menu_wrapper" data-kt-scroll="true"
                         data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-height="auto"
                         data-kt-scroll-dependencies="#kt_aside_logo, #kt_aside_footer"
                         data-kt-scroll-wrappers="#kt_aside_menu" data-kt-scroll-offset="0">
                        <!--begin::Menu-->
                        <div class="menu menu-column menu-title-gray-800 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-500" id="#kt_aside_menu" data-kt-menu="true" data-kt-menu-expand="false">
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
								<span class="menu-link">
									<span class="menu-icon">
										<!--begin::Svg Icon | path: icons/duotune/general/gen025.svg-->
										<span class="svg-icon svg-icon-2">
											<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path opacity="0.5" d="M18 2H9C7.34315 2 6 3.34315 6 5H8C8 4.44772 8.44772 4 9 4H18C18.5523 4 19 4.44772 19 5V16C19 16.5523 18.5523 17 18 17V19C19.6569 19 21 17.6569 21 16V5C21 3.34315 19.6569 2 18 2Z" fill="currentColor"/>
                                                <path fill-rule="evenodd" clip-rule="evenodd" d="M14.7857 7.125H6.21429C5.62255 7.125 5.14286 7.6007 5.14286 8.1875V18.8125C5.14286 19.3993 5.62255 19.875 6.21429 19.875H14.7857C15.3774 19.875 15.8571 19.3993 15.8571 18.8125V8.1875C15.8571 7.6007 15.3774 7.125 14.7857 7.125ZM6.21429 5C4.43908 5 3 6.42709 3 8.1875V18.8125C3 20.5729 4.43909 22 6.21429 22H14.7857C16.5609 22 18 20.5729 18 18.8125V8.1875C18 6.42709 16.5609 5 14.7857 5H6.21429Z" fill="currentColor"/>
                                            </svg>
										</span>
                                        <!--end::Svg Icon-->
									</span>
									<span class="menu-title">Contracts</span>
									<span class="menu-arrow"></span>
								</span>
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-add-contract.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">Add Contract</span>
                                        </a>
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-user-contracts.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">My Contracts</span>
                                        </a>
                                    </div>

                                </div>
                            </div>
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
								<span class="menu-link">
									<span class="menu-icon">
										<!--begin::Svg Icon | path: icons/duotune/general/gen025.svg-->
										<span class="svg-icon svg-icon-2">
											<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                 viewBox="0 0 24 24" fill="none">
												<rect x="2" y="2" width="9" height="9" rx="2" fill="currentColor"/>
												<rect opacity="0.3" x="13" y="2" width="9" height="9" rx="2"
                                                      fill="currentColor"/>
												<rect opacity="0.3" x="13" y="13" width="9" height="9" rx="2"
                                                      fill="currentColor"/>
												<rect opacity="0.3" x="2" y="13" width="9" height="9" rx="2"
                                                      fill="currentColor"/>
											</svg>
										</span>
                                        <!--end::Svg Icon-->
									</span>
									<span class="menu-title">Quotations</span>
									<span class="menu-arrow"></span>
								</span>
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-get-quotations.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">Get Quotation</span>
                                        </a>
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-user-quotations.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">My Quotations</span>
                                        </a>
                                    </div>

                                </div>
                            </div>
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
								<span class="menu-link">
									<span class="menu-icon">
										<!--begin::Svg Icon | path: icons/duotune/ecommerce/ecm007.svg-->
										<span class="svg-icon svg-icon-2">
											<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                 viewBox="0 0 24 24" fill="none">
												<path
                                                        d="M21 9V11C21 11.6 20.6 12 20 12H14V8H20C20.6 8 21 8.4 21 9ZM10 8H4C3.4 8 3 8.4 3 9V11C3 11.6 3.4 12 4 12H10V8Z"
                                                        fill="currentColor"/>
												<path
                                                        d="M15 2C13.3 2 12 3.3 12 5V8H15C16.7 8 18 6.7 18 5C18 3.3 16.7 2 15 2Z"
                                                        fill="currentColor"/>
												<path opacity="0.3"
                                                      d="M9 2C10.7 2 12 3.3 12 5V8H9C7.3 8 6 6.7 6 5C6 3.3 7.3 2 9 2ZM4 12V21C4 21.6 4.4 22 5 22H10V12H4ZM20 12V21C20 21.6 19.6 22 19 22H14V12H20Z"
                                                      fill="currentColor"/>
											</svg>
										</span>
                                        <!--end::Svg Icon-->
									</span>
									<span class="menu-title">Orders</span>
									<span class="menu-arrow"></span>
								</span>
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-user-orders.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">My Orders</span>
                                        </a>
                                    </div>

                                </div>
                            </div>
                            <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
								<span class="menu-link">
									<span class="menu-icon">
										<!--begin::Svg Icon | path: icons/duotune/general/gen025.svg-->
										<span class="svg-icon svg-icon-2">
											<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
														<path d="M13.0021 10.9128V3.01281C13.0021 2.41281 13.5021 1.91281 14.1021 2.01281C16.1021 2.21281 17.9021 3.11284 19.3021 4.61284C20.7021 6.01284 21.6021 7.91285 21.9021 9.81285C22.0021 10.4129 21.5021 10.9128 20.9021 10.9128H13.0021Z" fill="currentColor"></path>
														<path opacity="0.3" d="M11.0021 13.7128V4.91283C11.0021 4.31283 10.5021 3.81283 9.90208 3.91283C5.40208 4.51283 1.90209 8.41284 2.00209 13.1128C2.10209 18.0128 6.40208 22.0128 11.3021 21.9128C13.1021 21.8128 14.7021 21.3128 16.0021 20.4128C16.5021 20.1128 16.6021 19.3128 16.1021 18.9128L11.0021 13.7128Z" fill="currentColor"></path>
														<path opacity="0.3" d="M21.9021 14.0128C21.7021 15.6128 21.1021 17.1128 20.1021 18.4128C19.7021 18.9128 19.0021 18.9128 18.6021 18.5128L13.0021 12.9128H20.9021C21.5021 12.9128 22.0021 13.4128 21.9021 14.0128Z" fill="currentColor"></path>
                                            </svg>
										</span>
                                        <!--end::Svg Icon-->
									</span>
									<span class="menu-title">Reports</span>
									<span class="menu-arrow"></span>
								</span>
                                <div class="menu-sub menu-sub-accordion menu-active-bg">
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-quotations-reports.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">Quotations Report</span>
                                        </a>
                                    </div>
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-orders-reports.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">Orders Report</span>
                                        </a>
                                    </div>
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-offers-reports.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">Offers Report</span>
                                        </a>
                                    </div>
                                    <div class="menu-item">
                                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-system-report.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
                                            <span class="menu-title">Comprehensive Report</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
	                        <div data-kt-menu-trigger="click" class="menu-item menu-accordion">
								<span class="menu-link">
									<span class="menu-icon">
										<!--begin::Svg Icon | path: icons/duotune/general/gen025.svg-->
										<span class="svg-icon svg-icon-2">
											<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M16.0173 9H15.3945C14.2833 9 13.263 9.61425 12.7431 10.5963L12.154 11.7091C12.0645 11.8781 12.1072 12.0868 12.2559 12.2071L12.6402 12.5183C13.2631 13.0225 13.7556 13.6691 14.0764 14.4035L14.2321 14.7601C14.2957 14.9058 14.4396 15 14.5987 15H18.6747C19.7297 15 20.4057 13.8774 19.912 12.945L18.6686 10.5963C18.1487 9.61425 17.1285 9 16.0173 9Z" fill="currentColor"/>
												<rect opacity="0.3" x="14" y="4" width="4" height="4" rx="2" fill="currentColor"/>
												<path d="M4.65486 14.8559C5.40389 13.1224 7.11161 12 9 12C10.8884 12 12.5961 13.1224 13.3451 14.8559L14.793 18.2067C15.3636 19.5271 14.3955 21 12.9571 21H5.04292C3.60453 21 2.63644 19.5271 3.20698 18.2067L4.65486 14.8559Z" fill="currentColor"/>
												<rect opacity="0.3" x="6" y="5" width="6" height="6" rx="3" fill="currentColor"/>
											</svg>
										</span>
										<!--end::Svg Icon-->
									</span>
									<span class="menu-title">My Network</span>
									<span class="menu-arrow"></span>
								</span>
		                        <div class="menu-sub menu-sub-accordion menu-active-bg">
			                        <div class="menu-item">
				                        <a class="menu-link" href="<?php echo get_page_url_by_template('templates/page-service-providers-list.php');?>">
											<span class="menu-bullet">
												<span class="bullet bullet-dot"></span>
											</span>
					                        <span class="menu-title">Service Providers</span>
				                        </a>
			                        </div>
		                        </div>
	                        </div>
                        </div>
                    </div>
                </div>

                <div class="aside-footer flex-column-auto pt-5 pb-7 px-5" id="kt_aside_footer">
                    <a href="<?php echo wp_logout_url( get_permalink() ); ?>" class="btn btn-custom btn-primary w-100">
                        <span class="btn-label">Logout</span>
                        <!--begin::Svg Icon | path: icons/duotune/general/gen005.svg-->
                        <span class="svg-icon btn-icon svg-icon-2">
								<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
									<path opacity="0.3" d="M19 22H5C4.4 22 4 21.6 4 21V3C4 2.4 4.4 2 5 2H14L20 8V21C20 21.6 19.6 22 19 22ZM12.5 18C12.5 17.4 12.6 17.5 12 17.5H8.5C7.9 17.5 8 17.4 8 18C8 18.6 7.9 18.5 8.5 18.5L12 18C12.6 18 12.5 18.6 12.5 18ZM16.5 13C16.5 12.4 16.6 12.5 16 12.5H8.5C7.9 12.5 8 12.4 8 13C8 13.6 7.9 13.5 8.5 13.5H15.5C16.1 13.5 16.5 13.6 16.5 13ZM12.5 8C12.5 7.4 12.6 7.5 12 7.5H8C7.4 7.5 7.5 7.4 7.5 8C7.5 8.6 7.4 8.5 8 8.5H12C12.6 8.5 12.5 8.6 12.5 8Z" fill="currentColor"></path>
									<rect x="7" y="17" width="6" height="2" rx="1" fill="currentColor"></rect>
									<rect x="7" y="12" width="10" height="2" rx="1" fill="currentColor"></rect>
									<rect x="7" y="7" width="6" height="2" rx="1" fill="currentColor"></rect>
									<path d="M15 8H20L14 2V7C14 7.6 14.4 8 15 8Z" fill="currentColor"></path>
								</svg>
							</span>
                        <!--end::Svg Icon-->
                    </a>
                </div>
            </div>
            <!--end::Aside-->
        <?php
        }
        ?>

		<!--begin::Wrapper-->
		<div class="wrapper d-flex flex-column flex-row-fluid" id="kt_wrapper">
			<!--begin::Header-->
			<?php
			$user = wp_get_current_user();
			if(!in_array( 'shipping_company', $user->roles ) || in_array( 'administrator', $user->roles )){
			?>
                <div id="kt_header" class="header align-items-stretch">
                    <!--begin::Container-->
                    <div class="container-fluid d-flex align-items-stretch justify-content-end">
                        <!--begin::Aside mobile toggle-->
                        <div class="d-flex align-items-center d-lg-none ms-n2 me-2" title="Show aside menu">
                            <div class="btn btn-icon btn-active-light-primary w-30px h-30px w-md-40px h-md-40px"
                                 id="kt_aside_mobile_toggle">
                                <!--begin::Svg Icon | path: icons/duotune/abstract/abs015.svg-->
                                <span class="svg-icon svg-icon-1">
									<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                         fill="none">
										<path
                                                d="M21 7H3C2.4 7 2 6.6 2 6V4C2 3.4 2.4 3 3 3H21C21.6 3 22 3.4 22 4V6C22 6.6 21.6 7 21 7Z"
                                                fill="currentColor"/>
										<path opacity="0.3"
                                              d="M21 14H3C2.4 14 2 13.6 2 13V11C2 10.4 2.4 10 3 10H21C21.6 10 22 10.4 22 11V13C22 13.6 21.6 14 21 14ZM22 20V18C22 17.4 21.6 17 21 17H3C2.4 17 2 17.4 2 18V20C2 20.6 2.4 21 3 21H21C21.6 21 22 20.6 22 20Z"
                                              fill="currentColor"/>
									</svg>
								</span>
                                <!--end::Svg Icon-->
                            </div>
                        </div>
                        <!--end::Aside mobile toggle-->
                        <!--begin::Mobile logo-->
                        <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
                            <a href="<?php echo home_url();?>" class="d-lg-none">
                                <img alt="Logo" src="<?php echo get_template_directory_uri().'/'; ?>assets/images/logo_wayz.png" class="h-30px"/>
                            </a>
                        </div>
                        <!--end::Mobile logo-->
                        <!--begin::Wrapper-->
                        <div class="d-flex align-items-stretch justify-content-end flex-lg-grow-1">
                            <!--begin::Navbar-->
                            <div class="d-flex align-items-stretch" id="kt_header_nav">
                                <!--begin::Menu wrapper-->
                                <div class="d-flex align-items-stretch flex-shrink-0">

                                    <div class="menu menu-lg-rounded menu-column menu-lg-row menu-state-bg menu-title-gray-700 menu-state-title-primary menu-state-icon-primary menu-state-bullet-primary menu-arrow-gray-400 fw-bold my-5 my-lg-0 align-items-stretch"
                                         id="#kt_header_menu" data-kt-menu="true">
                                        <div class="menu-item me-lg-1">
                                            <a href="<?php echo get_page_url_by_template('templates/page-get-quotations.php')?>" class="menu-item menu-link py-3 ">Get Quotation</a>
                                        </div>
                                    </div>
                                    <!--begin::Notifications-->
                                    <div class="d-flex align-items-center ms-1 ms-lg-3 float-lg">
                                        <!--begin::Menu- wrapper-->
                                        <div class="btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px w-md-40px h-md-40px position-relative" data-kt-menu-trigger="click" data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
	                                        <?php
	                                        $bell_color = 'muted';
	                                        if(!empty($offers_notifications) || !empty($orders_notifications) || !empty($shipment_notifications)){
		                                        $bell_color = 'primary';
		                                        ?>
		                                        <span class="bullet bullet-dot bg-success h-10px w-10px position-absolute translate-middle top-0 start-50 animation-blink"></span>
		                                        <?php
	                                        }
	                                        ?>
                                            <span class="svg-icon svg-icon-1">
                                                <i class="bi bi-bell-fill fs-2x text-<?php echo $bell_color;?>"></i>
                                            </span>
                                        </div>
                                        <!--begin::Menu-->
                                        <div class="menu menu-sub menu-sub-dropdown menu-column w-350px w-lg-375px"
                                             data-kt-menu="true">
                                            <!--begin::Heading-->
                                            <div class="d-flex flex-column bgi-no-repeat rounded-top" style="background-image:url('<?php echo get_template_directory_uri().'/'; ?>assets/media/misc/pattern-1.jpg')">
                                                <!--begin::Title-->
                                                <h3 class="text-white fw-bold px-9 mt-10 mb-6">Notifications</h3>
                                                <!--end::Title-->
                                                <!--begin::Tabs-->
                                                <ul class="nav nav-line-tabs nav-line-tabs-2x nav-stretch fw-bold px-9">
                                                    <li class="nav-item">
                                                        <a class="nav-link text-white opacity-75 opacity-state-100 pb-4 active"
                                                           data-bs-toggle="tab" href="#kt_topbar_notifications_1">New Offers</a>
                                                    </li>

                                                    <li class="nav-item">
                                                        <a class="nav-link text-white opacity-75 opacity-state-100 pb-4"
                                                           data-bs-toggle="tab" href="#kt_topbar_notifications_2">Orders</a>
                                                    </li>

                                                    <li class="nav-item">
                                                        <a class="nav-link text-white opacity-75 opacity-state-100 pb-4"
                                                           data-bs-toggle="tab" href="#kt_topbar_notifications_3">Shipments</a>
                                                    </li>
                                                </ul>
                                                <!--end::Tabs-->
                                            </div>
                                            <!--end::Heading-->
                                            <!--begin::Tab content-->
                                            <div class="tab-content">
                                                <!--begin::Tab panel-->
                                                <div class="tab-pane fade  show active" id="kt_topbar_notifications_1" role="tabpanel">
                                                    <!--begin::Items-->
                                                    <div class="scroll-y mh-325px my-5 px-8 notification_list">
                                                        <?php
                                                        foreach ($offers_notifications as $notification){
	                                                        $datetime1 = new DateTime($notification['created_at']);
	                                                        $datetime2 = new DateTime(date( 'Y-m-d H:i:s', current_time( 'timestamp' ) ));
	                                                        $interval = $datetime1->diff($datetime2);

                                                            $time = intval($interval->format('%a'));
                                                            if($time == 0){
	                                                            $time =  intval($interval->format('%H'));
	                                                            if($time == 0){
		                                                            $time = intval($interval->format('%i'));
		                                                            $time.=' minutes';
	                                                            }else
                                                                    $time.=' hours';
                                                            }
                                                            else
	                                                            $time.=' days';

                                                            $link = get_page_url_by_template('templates/page-offers-list.php').'?quotation_id='.$notification['object_id'];

                                                        ?>
                                                        <!--begin::Item-->
                                                        <div class="d-flex flex-stack py-4 notification-item" data-notification-id="<?php echo $notification['id'];?>">
                                                            <!--begin::Section-->
                                                            <div class="d-flex align-items-center me-2">
                                                                <!--begin::Title-->
                                                                <a href="<?php echo $link;?>" class="text-gray-800 text-hover-primary fw-bold">
                                                                    <?php echo $notification['notification']; ?>
                                                                    <br>
                                                                    <span>
                                                                        <?php
                                                                        echo get_the_title($notification['object_id']);
                                                                        ?>
                                                                    </span>
                                                                </a>
                                                                <!--end::Title-->
                                                            </div>
                                                            <!--end::Section-->
                                                            <!--begin::Label-->
                                                            <span class="badge badge-light fs-8"><?php echo $time;?></span>
                                                            <!--end::Label-->
                                                        </div>
                                                        <!--end::Item-->
                                                        <?php
                                                        }
                                                        if(empty($offers_notifications)){
	                                                        ?>
                                                            <!--begin::Illustration-->
                                                            <div class="flex-grow-1 bgi-no-repeat bgi-size-contain bgi-position-x-center card-rounded-bottom h-200px mh-200px my-5 my-lg-12" style="background-image:url('<?php echo get_template_directory_uri().'/'; ?>assets/media/svg/illustrations/easy/2.svg')"></div>
                                                            <!--end::Illustration-->
	                                                        <?php
                                                        }
                                                        ?>
                                                    </div>
                                                    <!--end::Items-->
                                                </div>
                                                <!--end::Tab panel-->
                                                <!--begin::Tab panel-->
                                                <div class="tab-pane fade " id="kt_topbar_notifications_2" role="tabpanel">
                                                    <!--begin::Items-->
                                                    <div class="scroll-y mh-325px my-5 px-8 notification_list">
			                                            <?php
			                                            foreach ($orders_notifications as $notification){
				                                            $datetime1 = new DateTime($notification['created_at']);
				                                            $datetime2 = new DateTime(date( 'Y-m-d H:i:s', current_time( 'timestamp' ) ));
				                                            $interval = $datetime1->diff($datetime2);

				                                            $time = intval($interval->format('%a'));
				                                            if($time == 0){
					                                            $time =  intval($interval->format('%H'));
					                                            if($time == 0){
						                                            $time = intval($interval->format('%i'));
						                                            $time.=' minutes';
					                                            }else
						                                            $time.=' hours';
				                                            }
				                                            else
					                                            $time.=' days';

				                                            $link = get_page_url_by_template('templates/page-order-details.php').'?order_id='.$notification['object_id'];
				                                            ?>
                                                            <!--begin::Item-->
                                                            <div class="d-flex flex-stack py-4 notification-item" data-notification-id="<?php echo $notification['id'];?>">
                                                                <!--begin::Section-->
                                                                <div class="d-flex align-items-center me-2">
                                                                    <!--begin::Title-->
                                                                    <a href="<?php echo $link;?>" class="text-gray-800 text-hover-primary fw-bold">
							                                            <?php echo $notification['notification']; ?>
                                                                    </a>
                                                                    <!--end::Title-->
                                                                </div>
                                                                <!--end::Section-->
                                                                <!--begin::Label-->
                                                                <span class="badge badge-light fs-8"><?php echo $time;?></span>
                                                                <!--end::Label-->
                                                            </div>
                                                            <!--end::Item-->
				                                            <?php
			                                            }
			                                            if(empty($orders_notifications)){
				                                            ?>
                                                            <!--begin::Illustration-->
                                                            <div class="flex-grow-1 bgi-no-repeat bgi-size-contain bgi-position-x-center card-rounded-bottom h-200px mh-200px my-5 my-lg-12" style="background-image:url('<?php echo get_template_directory_uri().'/'; ?>assets/media/svg/illustrations/easy/2.svg')"></div>
                                                            <!--end::Illustration-->
				                                            <?php
			                                            }
			                                            ?>
                                                    </div>
                                                    <!--end::Items-->
                                                </div>
                                                <!--end::Tab panel-->
                                                <!--begin::Tab panel-->
                                                <div class="tab-pane fade " id="kt_topbar_notifications_3" role="tabpanel">
                                                    <!--begin::Items-->
                                                    <div class="scroll-y mh-325px my-5 px-8 notification_list">
			                                            <?php
			                                            foreach ($shipment_notifications as $notification){
				                                            $datetime1 = new DateTime($notification['created_at']);
				                                            $datetime2 = new DateTime(date( 'Y-m-d H:i:s', current_time( 'timestamp' ) ));
				                                            $interval = $datetime1->diff($datetime2);

				                                            $time = intval($interval->format('%a'));
				                                            if($time == 0){
					                                            $time =  intval($interval->format('%H'));
					                                            if($time == 0){
						                                            $time = intval($interval->format('%i'));
						                                            $time.=' minutes';
					                                            }else
						                                            $time.=' hours';
				                                            }
				                                            else
					                                            $time.=' days';

                                                            $link = get_page_url_by_template('templates/page-order-details.php').'?order_id='.$notification['object_id'];
				                                            ?>
                                                            <!--begin::Item-->
                                                            <div class="d-flex flex-stack py-4 notification-item" data-notification-id="<?php echo $notification['id'];?>">
                                                                <!--begin::Section-->
                                                                <div class="d-flex align-items-center me-2">
                                                                    <!--begin::Title-->
                                                                    <a href="<?php echo $link;?>" class="text-gray-800 text-hover-primary fw-bold">
							                                            <?php echo $notification['notification']; ?>
                                                                    </a>
                                                                    <!--end::Title-->
                                                                </div>
                                                                <!--end::Section-->
                                                                <!--begin::Label-->
                                                                <span class="badge badge-light fs-8"><?php echo $time;?></span>
                                                                <!--end::Label-->
                                                            </div>
                                                            <!--end::Item-->
				                                            <?php
			                                            }
			                                            if(empty($shipment_notifications)){
				                                            ?>
                                                            <!--begin::Illustration-->
                                                            <div class="flex-grow-1 bgi-no-repeat bgi-size-contain bgi-position-x-center card-rounded-bottom h-200px mh-200px my-5 my-lg-12" style="background-image:url('<?php echo get_template_directory_uri().'/'; ?>assets/media/svg/illustrations/easy/2.svg')"></div>
                                                            <!--end::Illustration-->
				                                            <?php
			                                            }
			                                            ?>
                                                    </div>
                                                    <!--end::Items-->
                                                </div>
                                                <!--end::Tab panel-->
                                            </div>
                                            <!--end::Tab content-->
                                        </div>
                                        <!--end::Menu-->
                                        <!--end::Menu wrapper-->
                                    </div>
                                    <!--end::Notifications-->
                                    <!--begin::Header menu toggle-->
                                    <div class="d-flex align-items-center d-lg-none ms-2 me-n3" title="Show header menu">
                                        <div class="btn btn-icon btn-active-light-primary w-30px h-30px w-md-40px h-md-40px"
                                             id="kt_header_menu_mobile_toggle">
                                            <!--begin::Svg Icon | path: icons/duotune/text/txt001.svg-->
                                            <span class="svg-icon svg-icon-1">
										<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                             viewBox="0 0 24 24" fill="none">
											<path d="M13 11H3C2.4 11 2 10.6 2 10V9C2 8.4 2.4 8 3 8H13C13.6 8 14 8.4 14 9V10C14 10.6 13.6 11 13 11ZM22 5V4C22 3.4 21.6 3 21 3H3C2.4 3 2 3.4 2 4V5C2 5.6 2.4 6 3 6H21C21.6 6 22 5.6 22 5Z"
                                                  fill="currentColor"></path>
											<path opacity="0.3"
                                                  d="M21 16H3C2.4 16 2 15.6 2 15V14C2 13.4 2.4 13 3 13H21C21.6 13 22 13.4 22 14V15C22 15.6 21.6 16 21 16ZM14 20V19C14 18.4 13.6 18 13 18H3C2.4 18 2 18.4 2 19V20C2 20.6 2.4 21 3 21H13C13.6 21 14 20.6 14 20Z"
                                                  fill="currentColor"></path>
										</svg>
									</span>
                                            <!--end::Svg Icon-->
                                        </div>
                                    </div>
                                    <!--end::Header menu toggle-->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
				<?php
			}
            ?>
			<!--end::Header-->
            <!--begin::Content-->
            <div class="content d-flex flex-column flex-column-fluid" id="kt_content">
                <div class="toolbar" id="kt_toolbar">
                    <!--begin::Container-->
                    <div id="kt_toolbar_container" class="container-fluid d-flex flex-stack">
                        <!--begin::Page title-->
                        <div data-kt-swapper="true" data-kt-swapper-mode="prepend" data-kt-swapper-parent="{default: '#kt_content_container', 'lg': '#kt_toolbar_container'}" class="page-title d-flex align-items-center flex-wrap me-3 mb-5 mb-lg-0">
                            <!--begin::Title-->
                            <h1 class="d-flex text-dark fw-bolder fs-3 align-items-center my-1">
                                <?php echo get_the_title();?>
                                <!--begin::Separator-->
                                <span class="h-20px border-1 border-gray-200 border-start ms-3 mx-2 me-1"></span>
                                <!--end::Separator-->
                                <!--begin::Description-->
                                <span class="text-muted fs-7 fw-bold mt-2"></span>
                                <!--end::Description-->
                            </h1>
                            <!--end::Title-->
                        </div>
                        <!--end::Page title-->
                        <!--begin::Actions-->
                        <?php
                        if($is_shipping_company){
                            ?>
                            <div class="d-flex align-items-center gap-2 gap-lg-3">
                                <a href="<?php echo wp_logout_url( get_permalink() ); ?>" class="btn btn-custom btn-primary w-100">
                                    Logout
                                </a>
                            </div>
                        <?php
                        }
                        ?>
                        <!--end::Actions-->
                    </div>
                    <!--end::Container-->
                </div>