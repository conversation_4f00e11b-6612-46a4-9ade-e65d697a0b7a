# QA Test Plan - WordPress Freight Management System (WAYZ)

## System Overview
The WAYZ system is a comprehensive WordPress-based freight management platform that facilitates quotation requests, offer management, order processing, and shipment tracking between customers and service providers.

## Core System Components

### 1. User Management & Roles
- **Customer**: Can create quotations, manage addresses, view offers, accept orders
- **Service Provider (Shipping Company)**: Can view quotations, submit offers, manage shipments
- **SP Contact**: Service provider contact with limited permissions
- **Administrator**: Full system access and overview capabilities

### 2. Main Entities
- **Quotations**: Freight requests with pickup/destination details
- **Offers**: Service provider responses to quotations
- **Orders**: Accepted offers converted to shipments
- **Contracts**: Pre-negotiated agreements between customers and providers
- **Organizations**: Customer company groupings
- **Addresses**: Pickup and delivery locations

## Test Categories

## 1. Authentication & Authorization Tests

### 1.1 User Registration & Login
**Test Cases:**
- [ ] **TC-001**: Verify customer registration with valid data
- [ ] **TC-002**: Verify service provider registration
- [ ] **TC-003**: Test login with valid credentials
- [ ] **TC-004**: Test login with invalid credentials
- [ ] **TC-005**: Test password reset functionality
- [ ] **TC-006**: Verify hash-based auto-login from email links

**Expected Results:**
- Users can register and login successfully
- Invalid credentials are rejected
- Password reset emails are sent and functional
- Auto-login from email links works correctly

### 1.2 Role-Based Access Control
**Test Cases:**
- [ ] **TC-007**: Verify customer can only access customer pages
- [ ] **TC-008**: Verify service provider can only access SP pages
- [ ] **TC-009**: Test admin can access all system areas
- [ ] **TC-010**: Verify SP contact inherits service provider permissions
- [ ] **TC-011**: Test unauthorized access redirects to no-permissions page

## 2. Quotation Management Tests

### 2.1 Quotation Creation
**Test Cases:**
- [ ] **TC-012**: Create sea freight quotation with FCL containers
- [ ] **TC-013**: Create air freight quotation with cargo details
- [ ] **TC-014**: Test quotation with pickup address selection
- [ ] **TC-015**: Test quotation with Google Maps location picker
- [ ] **TC-016**: Verify mandatory field validation
- [ ] **TC-017**: Test quotation draft saving functionality
- [ ] **TC-018**: Verify quotation publishing triggers email notifications

**Test Data Requirements:**
- Valid origin/destination countries and ports
- Container specifications (20ft, 40ft, 40ft HC)
- Air freight cargo dimensions and weight
- Pickup and delivery addresses

### 2.2 Quotation Viewing & Management
**Test Cases:**
- [ ] **TC-019**: Verify quotation details display correctly
- [ ] **TC-020**: Test quotation editing by owner
- [ ] **TC-021**: Verify quotation history logging
- [ ] **TC-022**: Test quotation search and filtering
- [ ] **TC-023**: Verify organization-based quotation access

## 3. Offer Management Tests

### 3.1 Offer Submission
**Test Cases:**
- [ ] **TC-024**: Service provider submits valid offer
- [ ] **TC-025**: Test offer with validity days
- [ ] **TC-026**: Test offer with transit time
- [ ] **TC-027**: Test offer with port free days
- [ ] **TC-028**: Verify offer price validation (USD/SAR)
- [ ] **TC-029**: Test contract-based call-off offers
- [ ] **TC-030**: Verify offer submission triggers customer notification

### 3.2 Offer Management
**Test Cases:**
- [ ] **TC-031**: Customer views all offers for quotation
- [ ] **TC-032**: Customer accepts an offer
- [ ] **TC-033**: Customer requests new offer from provider
- [ ] **TC-034**: Service provider declines to offer
- [ ] **TC-035**: Test offer expiration handling
- [ ] **TC-036**: Verify offer status updates (pending/accepted/rejected/cancelled)

## 4. Order & Shipment Management Tests

### 4.1 Order Creation
**Test Cases:**
- [ ] **TC-037**: Verify order creation from accepted offer
- [ ] **TC-038**: Test WooCommerce order integration
- [ ] **TC-039**: Verify order pricing calculation
- [ ] **TC-040**: Test contract tracking for call-off orders
- [ ] **TC-041**: Verify order assignment to service provider

### 4.2 Shipment Tracking
**Test Cases:**
- [ ] **TC-042**: Service provider updates shipment status
- [ ] **TC-043**: Test shipment status progression (origin warehouse → in transit → delivered)
- [ ] **TC-044**: Verify shipment data updates
- [ ] **TC-045**: Test file upload/sharing between parties
- [ ] **TC-046**: Verify shipment history logging

## 5. Contract Management Tests

### 5.1 Contract Creation & Management
**Test Cases:**
- [ ] **TC-047**: Create money-based contract
- [ ] **TC-048**: Create container-based contract
- [ ] **TC-049**: Test contract trip configuration
- [ ] **TC-050**: Verify contract expiration handling
- [ ] **TC-051**: Test contract remaining balance tracking
- [ ] **TC-052**: Verify contract activity logging

### 5.2 Contract Usage
**Test Cases:**
- [ ] **TC-053**: Test call-off order against active contract
- [ ] **TC-054**: Verify contract balance deduction
- [ ] **TC-055**: Test contract coverage validation
- [ ] **TC-056**: Verify contract deactivation when exhausted

## 6. Address Management Tests

**Test Cases:**
- [ ] **TC-057**: Add new shipping address
- [ ] **TC-058**: Edit existing address
- [ ] **TC-059**: Test address uniqueness validation
- [ ] **TC-060**: Verify organization-wide address sharing
- [ ] **TC-061**: Test address selection in quotations

## 7. Service Provider Management Tests

**Test Cases:**
- [ ] **TC-062**: Add new service provider company
- [ ] **TC-063**: Edit service provider details
- [ ] **TC-064**: Add service provider contacts
- [ ] **TC-065**: Test public vs private service providers
- [ ] **TC-066**: Verify service provider search functionality

## 8. Reporting & Analytics Tests

### 8.1 Dashboard Statistics
**Test Cases:**
- [ ] **TC-067**: Verify active quotations count
- [ ] **TC-068**: Test active offers statistics
- [ ] **TC-069**: Verify shipments count display
- [ ] **TC-070**: Test savings calculation accuracy

### 8.2 Reports Generation
**Test Cases:**
- [ ] **TC-071**: Generate quotations report
- [ ] **TC-072**: Generate orders report
- [ ] **TC-073**: Generate offers report
- [ ] **TC-074**: Test report filtering and search
- [ ] **TC-075**: Verify report export functionality (Excel)

## 9. Notification System Tests

### 9.1 Email Notifications
**Test Cases:**
- [ ] **TC-076**: New quotation notification to service providers
- [ ] **TC-077**: New offer notification to customers
- [ ] **TC-078**: Offer acceptance notification
- [ ] **TC-079**: File request/sharing notifications
- [ ] **TC-080**: Test email template customization

### 9.2 In-System Notifications
**Test Cases:**
- [ ] **TC-081**: Verify notification creation
- [ ] **TC-082**: Test notification marking as read
- [ ] **TC-083**: Verify notification display in dashboard

## 10. File Management Tests

**Test Cases:**
- [ ] **TC-084**: Upload files to orders
- [ ] **TC-085**: Download shared files
- [ ] **TC-086**: Delete uploaded files (within time limit)
- [ ] **TC-087**: Request files from service provider
- [ ] **TC-088**: Verify file access permissions

## 11. Integration Tests

### 11.1 WooCommerce Integration
**Test Cases:**
- [ ] **TC-089**: Verify order creation in WooCommerce
- [ ] **TC-090**: Test order status synchronization
- [ ] **TC-091**: Verify pricing integration
- [ ] **TC-092**: Test customer data synchronization