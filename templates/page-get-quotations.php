<?php
/**
 * Template Name: Get Quotations
 */

use <PERSON>z\Admin\Settings;
use <PERSON>z\Controllers\Addresses;
use <PERSON>z\Controllers\Contracts;
use <PERSON>z\Controllers\ServiceProvider;
use Wayz\Controllers\Quotations;
use <PERSON>z\Utils;

defined( 'ABSPATH' ) || exit;

get_header( '' );

$user_id         = get_current_user_id();
$quotationsObj   = new Quotations();
$addressObj      = new Addresses();
$serviceProvider = new ServiceProvider();
$countries       = wayz_get_countries();
$addresses       = $addressObj->list( get_current_user_id() );
$companies       = $serviceProvider->migrate_old_data( $user_id );

if ( isset( $_POST['add_new_address'] ) ) {
	$addressObj->add( $_POST, $user_id );
}

if ( isset( $_GET['quotation_id'] ) ) {
	$quotation_status = get_post_status( $_GET['quotation_id'] );
}

if ( isset( $_POST['send_quotation'] ) ) {
	$air_weight        = $quotation['air_weight'] ?? [ '' ];
	$air_weight_uom    = $quotation['air_weight_uom'] ?? [ '' ];
	$air_dimensions_l = $quotation['air_dimensions_l'] ?? [ '' ];
	$air_dimensions_w = $quotation['air_dimensions_w'] ?? [ '' ];
	$air_dimensions_h = $quotation['air_dimensions_h'] ?? [ '' ];
	$args = [
		'shipment_type'           => $_POST['shipment_type'],
		'load_type'               => $_POST['load_type'],
		'pallets'                 => $_POST['pallets'] ?? [],
		'quotation_title'         => $_POST['quotation_title'],
		'container-specification' => $_POST['container-specification'] ?? [],
		'quantity'                => $_POST['quantity'] ?? [],
		'incoterms'               => $_POST['incoterms'],
		'pickup_address'          => Utils::get_pickup_location( $_POST ),
		'origin_country'          => $_POST['origin_country'],
		'origin_port'             => $_POST['origin_port'],
		'pick_up_date'            => $_POST['pick_up_date'],
		'destination_location'    => Utils::get_destination_location( $_POST ),
		'destination_country'     => $_POST['destination_country'],
		'destination_port'        => $_POST['destination_port'],
		'insurance'               => $_POST['insurance'] ?? '',
		'customs-clearance'       => $_POST['customs-clearance'] ?? '',
		'fright_companies'        => $_POST['fright_companies'] ?? [],
		'send_wayz_providers'     => $_POST['send_wayz_providers'] ?? 'no',
		'note'                    => $_POST['note'],
		'air_pallets'             => $_POST['air_pallets'] ?? [],
		'air_weight'              => $_POST['air_weight'] ?? [],
		'air_weight_uom'          => $_POST['air_weight_uom'] ?? [],
		'air_dimensions_l'        => $_POST['air_dimensions_l'] ?? [],
		'air_dimensions_w'        => $_POST['air_dimensions_w'] ?? [],
		'air_dimensions_h'        => $_POST['air_dimensions_h'] ?? [],
		'validity_days'           => $_POST['validity_days'],
		'deliver_by_date'         => $_POST['deliver_by_date'],
	];

	$is_update_request = isset( $_GET['quotation_id'], $_GET['action'] ) && 'draft' === $quotation_status && 'edit' == $_GET['action'];
	if ( $is_update_request ) {
		$post_id = $quotationsObj->update( $args, $_GET['quotation_id'] );
	} else {
		$post_id = $quotationsObj->create( $args, $user_id );
	}
}

$quotation = array();
if ( isset( $_GET['quotation_id'] ) && ! isset( $_POST['send_quotation'] ) ) {
	$quotation = $quotationsObj->get( $_GET['quotation_id'] );
}

if ( isset( $_POST['submit_quotation_confirm'] ) ) {
	$quotationsObj->submit( $_POST['submit_quotation_confirm'] );
}

$is_quotation_request            = isset( $_POST['send_quotation'] ) || isset( $_POST['submit_quotation_confirm'] );
$is_draft_or_duplicate           = ! isset( $quotation_status ) || 'draft' == $quotation_status || ( isset( $_GET['action'] ) && 'duplicate' == $_GET['action'] );
$quotation_created               = isset( $_POST['send_quotation'], $post_id ) && $post_id;
$quotation_confirmed             = isset( $_POST['submit_quotation_confirm'] );
$try_to_edit_published_quotation = isset( $_GET['quotation_id'], $_GET['action'], $quotation_status ) && 'edit' === $_GET['action'] && 'draft' !== $quotation_status;
?>
	<!--begin::Post-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<?php

			if ( ! $is_quotation_request && $is_draft_or_duplicate ) {

				if ( isset( $post_error_msg ) ) {
					wc_get_template( 'get-quotation/notice/error.php', array( 'message' => $post_error_msg ), '', get_template_directory() . '/templates/' );
				}

				?>
				<!--begin::Layout-->
				<div class="d-flex flex-column flex-lg-row">
					<!--begin::Content-->
					<div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
						<!--begin::Card-->
						<div class="card">
							<!--begin::Card body-->
							<div class="card-body p-12">
								<!--begin::Form-->
								<form action="" id="kt_invoice_form" method="post">
									<?php wc_get_template( 'get-quotation/form/basic.php', array( 'quotation' => $quotation ), '', get_template_directory() . '/templates/' ); ?>
									<!--begin::Separator-->
									<div class="separator separator-dashed my-10"></div>
									<!--end::Separator-->
									<?php wc_get_template( 'get-quotation/form/shipment-type.php', array( 'quotation' => $quotation ), '', get_template_directory() . '/templates/' ); ?>
									<!--begin::Separator-->
									<div class="separator separator-dashed my-10"></div>
									<!--end::Separator-->
									<?php wc_get_template( 'get-quotation/form/load-type.php', array( 'quotation' => $quotation ), '', get_template_directory() . '/templates/' ); ?>
									<!--begin::Wrapper-->
									<div class="mb-0">
										<!--begin::Table wrapper-->
										<div class="table-responsive mb-10">
											<?php
											wc_get_template( 'get-quotation/form/sea-shipment.php', array( 'quotation' => $quotation ), '', get_template_directory() . '/templates/' );

											if ( Settings::get_setting( 'air_fight_feature' ) ) {
												wc_get_template( 'get-quotation/form/air-shipment.php', array( 'quotation' => $quotation ), '', get_template_directory() . '/templates/' );
											}
											?>
										</div>
										<!--end::Table-->
									</div>
									<!--end::Wrapper-->
									<?php
									// Incoterms
									wc_get_template( 'get-quotation/form/incoterms.php', array( 'quotation' => $quotation ), '', get_template_directory() . '/templates/' );

									// Pickup Location
									wc_get_template( 'get-quotation/form/pickup-location.php', array( 'quotation' => $quotation, 'addresses' => $addresses, 'countries' => $countries ), '', get_template_directory() . '/templates/' );

									// Destination
									wc_get_template( 'get-quotation/form/destination.php', array( 'quotation' => $quotation, 'addresses' => $addresses, 'countries' => $countries ), '', get_template_directory() . '/templates/' );

									// Service Providers
									wc_get_template( 'get-quotation/form/service-providers.php', array( 'quotation' => $quotation, 'companies' => $companies ), '', get_template_directory() . '/templates/' );

									// Service Providers
									wc_get_template( 'get-quotation/form/note.php', array( 'quotation' => $quotation ), '', get_template_directory() . '/templates/' );
									?>
									<div class="text-center">
										<a href="<?php echo get_page_url_by_template( 'templates/page-user-quotations.php' ); ?>" class="btn btn-light me-3">Cancel</a>
										<button id="save_draft" type="submit" class="btn btn-primary">
											Save Draft
										</button>
									</div>
								</form>
								<!--end::Form-->
							</div>
							<!--end::Card body-->
						</div>
						<!--end::Card-->
					</div>
					<?php
					wc_get_template( 'get-quotation/new-address.php', array( 'countries' => $countries ), '', get_template_directory() . '/templates/' );
					wc_get_template( 'get-quotation/sidebar.php', array( 'quotation' => $quotation, 'service_provider' => $serviceProvider ), '', get_template_directory() . '/templates/' );
					?>
				</div>
				<!--end::Layout-->
				<?php
			} elseif ( $quotation_created ) {
				?>
				<!--begin::Card-->
				<div class="card">
					<!--begin::Card body-->
					<div class="card-body p-12">
						<div class="row g-5 mb-5 mb-lg-15">
							<?php
							$service_providers = get_post_meta( $post_id, '_fright_companies', true );
							if ( is_array( $service_providers ) ) {
								foreach ( $service_providers as $email ) {
									$company = get_user_by( 'email', $email );

									if ( ! $company ) {
										continue;
									}

									$contracts_obj = new Contracts();
									$contracts     = $contracts_obj->get_contracts( get_current_user_id(), $company->ID );
									$coverage      = false;
									foreach ( $contracts as $contract ) {
										if ( $contracts_obj->check_contract_coverage( $contract['id'], $post_id ) ) {
											$coverage = true;
											update_post_meta( $post_id, '_company_' . $company->ID . '_contract_id',
												$contract['id'] );
											break;
										}
									}
									if ( ! $coverage ) {
										$text = 'We will send quotation request';
									} else {
										$text = 'You have contract and we will send call-off';
									}
									?>
									<!--begin::Col-->
									<div class="col-sm-6 pe-lg-10">
										<!--begin::Phone-->
										<div class="text-center bg-light card-rounded d-flex flex-column justify-content-center p-10 h-100">
											<h1 class="text-dark fw-bold my-5"><?php echo $company->display_name; ?></h1>
											<div class="text-gray-700 fw-semibold fs-2"><?php echo $email; ?></div>
											<div class="text-gray-700 fw-semibold fs-2"><?php echo $text; ?></div>
										</div>
										<!--end::Phone-->
									</div>
									<!--end::Col-->
									<?php
								}
							}

							if ( 'yes' === get_post_meta( $post_id, '_send_to_wayz_providers', true ) ) {
								$providers = $serviceProvider->get_public_service_providers();
								foreach ( $providers as $provider ) {
									?>
									<div class="col-sm-6 pe-lg-10">
										<!--begin::Phone-->
										<div class="text-center bg-light card-rounded d-flex flex-column justify-content-center p-10 h-100">
											<h1 class="text-dark fw-bold my-5"><?php echo $provider->display_name; ?></h1>
											<div class="text-gray-700 fw-semibold fs-2"><?php echo __( 'We will send quotation request to our provider',
													'wayz' ); ?></div>
										</div>
										<!--end::Phone-->
									</div>
									<?php
								}
							}
							?>
						</div>
						<div class="row g-5 mb-5 mb-lg-15 mt-3">
							<form action="" method="post">
								<input type="hidden" name="submit_quotation_confirm" value="<?php echo $post_id; ?>"/>
								<div class="text-center">
									<a class="btn btn-light me-3" href="<?php echo get_page_url_by_template( 'templates/page-get-quotations.php' ) . '?quotation_id=' . $post_id; ?>">
										Edit
									</a>
									<button type="submit" class="btn btn-primary" id="quotationForm_submit_button">
										<!--begin::Svg Icon | path: icons/duotune/general/gen016.svg-->
										<span class="svg-icon svg-icon-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                <path d="M15.43 8.56949L10.744 15.1395C10.6422 15.282 10.5804 15.4492 10.5651 15.6236C10.5498 15.7981 10.5815 15.9734 10.657 16.1315L13.194 21.4425C13.2737 21.6097 13.3991 21.751 13.5557 21.8499C13.7123 21.9488 13.8938 22.0014 14.079 22.0015H14.117C14.3087 21.9941 14.4941 21.9307 14.6502 21.8191C14.8062 21.7075 14.9261 21.5526 14.995 21.3735L21.933 3.33649C22.0011 3.15918 22.0164 2.96594 21.977 2.78013C21.9376 2.59432 21.8452 2.4239 21.711 2.28949L15.43 8.56949Z" fill="currentColor"/>
                                                <path opacity="0.3" d="M20.664 2.06648L2.62602 9.00148C2.44768 9.07085 2.29348 9.19082 2.1824 9.34663C2.07131 9.50244 2.00818 9.68731 2.00074 9.87853C1.99331 10.0697 2.04189 10.259 2.14054 10.4229C2.23919 10.5869 2.38359 10.7185 2.55601 10.8015L7.86601 13.3365C8.02383 13.4126 8.19925 13.4448 8.37382 13.4297C8.54839 13.4145 8.71565 13.3526 8.85801 13.2505L15.43 8.56548L21.711 2.28448C21.5762 2.15096 21.4055 2.05932 21.2198 2.02064C21.034 1.98196 20.8409 1.99788 20.664 2.06648Z" fill="currentColor"/>
                                            </svg>
                                        </span>
										<!--end::Svg Icon-->Submit the Quotation
									</button>
								</div>
							</form>
						</div>
					</div>
					<!--end::Card body-->
				</div>
				<!--end::Card-->
				<?php
			} elseif ( $quotation_confirmed ) {
				wc_get_template( 'get-quotation/notice/confirmed.php', array(), '', get_template_directory() . '/templates/' );
			} elseif ( $try_to_edit_published_quotation ) {
				wc_get_template( 'get-quotation/notice/blocker.php', array( 'message' => 'Sorry, you cant edit published quotations request.' ), '', get_template_directory() . '/templates/' );
			}
			?>
		</div>
		<!--end::Container-->
	</div>
	<!--end::Post-->
<?php
get_footer();