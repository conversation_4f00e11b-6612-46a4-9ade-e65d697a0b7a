<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( empty( $user_id ) ) {
	return;
}

use <PERSON>z\Controllers\Contracts;
?>
<div class="card card-flush" id="userContracts">
	<!--begin::Card header-->
	<div class="card-header align-items-center py-5 gap-2 gap-md-5">
		<!--begin::Card title-->
		<div class="card-title">
			<!--begin::Search-->
			<div class="d-flex align-items-center position-relative my-1">
				<!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
				<span class="svg-icon svg-icon-1 position-absolute ms-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
                                    <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor" />
                                </svg>
                            </span>
				<!--end::Svg Icon-->
				<input type="text" data-kt-ecommerce-order-filter="search" class="form-control form-control-solid w-250px ps-14" placeholder="Search Contract" />
			</div>
			<!--end::Search-->
		</div>
		<!--end::Card title-->
		<!--begin::Card toolbar-->
		<div class="card-toolbar">
			<!--begin::Add customer-->
			<a href="<?php echo get_page_url_by_template('templates/page-add-contract.php');?>" class="btn btn-primary">Add Contract</a>
			<!--end::Add customer-->
		</div>
		<!--end::Card toolbar-->
	</div>
	<!--end::Card header-->
	<!--begin::Card body-->
	<div class="card-body pt-0">
		<!--begin::Table-->
		<?php
		$contracts      = ( new Contracts() )->get_contracts( $user_id );
		if ( ! empty( $contracts ) ) {
			?>
			<table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
				<!--begin::Table head-->
				<thead>
				<input id="filter_column" value="4" disabled hidden>
				<!--begin::Table row-->
				<tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
					<th data-kt-ecommerce-order-filter="order_id">ID</th>
					<th class="min-w-250px">Contract</th>
					<th class="min-w-150px">Tracking type</th>
					<th class="min-w-150px">Allowed Trips</th>
					<th class="text-end">Status</th>
					<th class="text-end">Actions</th>
				</tr>
				<!--end::Table row-->
				</thead>
				<!--end::Table head-->
				<!--begin::Table body-->
				<tbody class="fw-bold text-gray-600 table-striped ">
				<!--begin::Table row-->
				<?php
				$contracts_link = get_page_url_by_template( 'templates/page-add-contract.php' );
				foreach ( $contracts as $contract ){
					?>
					<tr>
						<td><?php echo $contract['id'];?></td>
						<td>
							<div class="ms-5">
								<!--begin::Title-->
								<a href="<?php echo $contracts_link.'?contract_id='.$contract['id'];?>" class="text-gray-800 text-hover-primary fs-5 fw-bolder mb-1"><?php echo $contract['title'];?></a>
								<!--end::Title-->
								<!--begin::Description-->
								<div class="d-flex flex-column mt-2">
									<div class="h-8px bg-light rounded mb-3" style="width: 80%;">
										<div class="bg-success rounded h-8px" role="progressbar" style="width: <?php echo $contract['consumed'];?>%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
									</div>
									<div class="fw-semibold text-gray-600"><?php echo $contract['remaining_text'];?></div>
								</div>
								<!--end::Description-->
							</div>
						</td>
						<td>
							<?php echo $contract['type'];?>
						</td>
						<td>
							<?php
							foreach ( $contract['trips'] as $trip ) {
								foreach ( $trip as $key => $value ) {
									echo ucfirst( $key ) . ': ' . $value;
									if ( 'cost' === $key ) {
										echo ' '.$contract['currency'];
									} else {
										echo " || ";
									}
								}
								echo '<br>';
							}
							?>
						</td>
						<td class="text-end" data-order="<?php echo $contract['status'];?>">
							<!--begin::Badges-->
							<div class="badge badge-light-<?php if ( 'active' == $contract['status'] ) { echo 'success'; } else { echo 'danger'; }?>"><?php echo $contract['status'];?></div>
							<!--end::Badges-->
						</td>
						<td class="text-end">
							<?php
							if ( 'active' == $contract['status'] ){
								$deactivate_link = add_query_arg( [
									'deactivate'    => 'deactivate',
									'contract_id'   => $contract['id']
								] );
								?>
								<a href="<?php echo $deactivate_link;?>" class="btn btn-danger fs-8">Deactivate</a>
								<?php
							}
							?>
						</td>
					</tr>
					<?php
				}
				?>
				<!--end::Table row-->
				</tbody>
				<!--end::Table body-->
			</table>
			<?php
		}  else {
			?>
			<div class="mb-2">
				<!--begin::Title-->
				<h2 class="fw-bold text-gray-400 text-center lh-lg">
					There is no Contracts
				</h2>
				<!--end::Title-->
				<!--begin::Illustration-->
				<div class="flex-grow-1 bgi-no-repeat bgi-size-contain bgi-position-x-center card-rounded-bottom h-200px mh-200px my-5 my-lg-12" style="background-image:url('<?php echo get_template_directory_uri().'/'; ?>assets/media/svg/illustrations/easy/1.svg')"></div>
				<!--end::Illustration-->
			</div>
			<?php
		}
		?>
		<!--end::Table-->
	</div>
	<!--end::Card body-->
</div>
