<?php

use Wayz\Reports\QuotationsReport;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $overview ) ) {
	$overview = false;
}

if ( empty( $user_id ) ) {
	$user_id = get_current_user_id();

	if ( current_user_can( 'manage_options' ) ) {
		$overview = true;
	}
}
?>
<div id="quotationsReport" class="card card-flush">
	<!--begin::Card header-->
	<div class="card-header align-items-center py-5 gap-2 gap-md-5">
		<!--begin::Card title-->
		<div class="card-title">
			<!--begin::Search-->
			<div class="d-flex align-items-center position-relative my-1">
				<!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
				<span class="svg-icon svg-icon-1 position-absolute ms-4">
					<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
						<rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"/>
						<path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"/>
					</svg>
				</span>
				<!--end::Svg Icon-->
				<input type="text" data-kt-ecommerce-order-filter="search" class="form-control form-control-solid w-250px ps-14" placeholder="Search Quotation"/>
			</div>
			<!--end::Search-->
		</div>
		<!--end::Card title-->
		<!--begin::Card toolbar-->
		<div class="card-toolbar flex-row-fluid justify-content-end gap-5">
			<!--begin::Flatpickr-->
			<div class="input-group w-250px">
				<input class="form-control form-control-solid rounded rounded-end-0" placeholder="Pick date range" id="kt_ecommerce_sales_flatpickr"/>
				<button class="btn btn-icon btn-light" id="kt_ecommerce_sales_flatpickr_clear">
					<!--begin::Svg Icon | path: icons/duotune/arrows/arr088.svg-->
					<span class="svg-icon svg-icon-2">
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
							<rect opacity="0.5" x="7.05025" y="15.5356" width="12" height="2" rx="1" transform="rotate(-45 7.05025 15.5356)" fill="currentColor"/>
							<rect x="8.46447" y="7.05029" width="12" height="2" rx="1" transform="rotate(45 8.46447 7.05029)" fill="currentColor"/>
						</svg>
					</span>
					<!--end::Svg Icon-->
				</button>
			</div>
			<!--end::Flatpickr-->
			<div class="w-100 mw-150px">
				<!--begin::Select2-->
				<select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Status" data-kt-ecommerce-order-filter="status">
					<option></option>
					<option value="all">All</option>
					<option value="Pending">Pending</option>
					<option value="Completed">Completed</option>
				</select>
				<!--end::Select2-->
			</div>
			<a href="#" id="reportExport" class="btn btn-primary">Export</a>
		</div>
		<!--end::Card toolbar-->
	</div>
	<!--end::Card header-->
	<!--begin::Card body-->
	<div class="card-body pt-0">
		<!--begin::Table-->
		<table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
			<!--begin::Table head-->
			<thead>
			<!--begin::Table row-->
			<input id="filter_column" value="15" disabled hidden>
			<input id="date_column" value="14" disabled hidden>
			<tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
				<th class="min-w-100px">Quotation ID</th>
				<th class="min-w-175px">Title</th>
				<th class="min-w-175px">Shipment type</th>
				<th class="min-w-175px">Load type</th>
				<th class="min-w-150px">Offers count</th>
				<th class="min-w-150px">Max offer price</th>
				<th class="min-w-150px">Min offer price</th>
				<th class="min-w-150px">Offer prices range</th>
				<th class="min-w-150px">Origin Country</th>
				<th class="min-w-150px">Origin Port</th>
				<th class="min-w-200px">Destination Location</th>
				<th class="min-w-150px">Destination Port</th>
				<th class="min-w-150px">Incoterms</th>
				<th class="min-w-150px">Customs Clearance</th>
				<th class="min-w-150px">Insurance</th>
				<th class="min-w-150px">Pickup Date</th>
				<th class="min-w-150px">Date</th>
				<th class="min-w-100px">Ordered?</th>
			</tr>
			<!--end::Table row-->
			</thead>
			<!--end::Table head-->
			<!--begin::Table body-->
			<tbody class="fw-bold text-gray-600">
			<?php
			$quotations_report = ( new QuotationsReport() )->list( $user_id, $overview );
			$quotations        = $quotations_report['quotations'];
			foreach ( $quotations as $quotation ) {
				?>
				<!--begin::Table row-->
				<tr>
					<!--begin::Quotation ID=-->
					<td data-kt-ecommerce-order-filter="order_id">
						<a href="<?php echo $quotation['link']; ?>" class="text-gray-800 text-hover-primary fw-bolder">
							<?php echo $quotation['id']; ?>
						</a>
					</td>
					<!--end::Quotation ID=-->
					<!--begin::Title=-->
					<td>
						<a href="<?php echo $quotation['link']; ?>" class="text-gray-800 text-hover-primary fs-5 fw-bolder">
							<?php echo $quotation['title']; ?>
						</a>
					</td>
					<!--end::Title=-->
					<!--begin::Shipment Type=-->
					<td>
						<span class="fw-bolder">
							<?php echo $quotation['shipment_type'] ?? '-'; ?>
						</span>
					</td>
					<!--end::Shipment Type=-->
					<!--begin::Load Type=-->
					<td>
						<span class="fw-bolder">
							<?php echo $quotation['load_type'] ?? '-'; ?>
						</span>
					</td>
					<!--end::Load Type=-->
					<!--begin::Offers count=-->
					<td>
						<span class="fw-bolder">
							<?php echo $quotation['offers_count']; ?>
						</span>
					</td>
					<!--end::Offers count=-->
					<!--begin::Max offer price=-->
					<td>
						<span class="fw-bolder">
							<?php echo $quotation['maximum_offer']; ?>
						</span>
					</td>
					<!--end::Max offer price=-->
					<!--begin::Min offer price=-->
					<td>
						<span class="fw-bolder">
							<?php echo $quotation['minimum_offer']; ?>
						</span>
					</td>
					<!--end::Min offer price=-->
					<!--begin::offer price range=-->
					<td>
						<span class="fw-bolder">
							<?php echo $quotation['offers_price_range']; ?>
						</span>
					</td>
					<!--end::offer price range=-->
					<!--begin::Date Added=-->
					<td data-order="<?php echo $quotation['origin_country']; ?>">
						<span class="fw-bolder">
							<?php echo $quotation['origin_country']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Date Added=-->
					<td data-order="<?php echo $quotation['origin_port']; ?>">
						<span class="fw-bolder">
							<?php echo $quotation['origin_port']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Date Added=-->
					<td>
						<span class="fw-bolder">
							<?php echo $quotation['destination_location']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Date Added=-->
					<td data-order="<?php echo $quotation['destination_port']; ?>">
						<span class="fw-bolder">
							<?php echo $quotation['destination_port']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Date Added=-->
					<td data-order="<?php echo $quotation['incoterms']; ?>">
						<span class="fw-bolder">
							<?php echo $quotation['incoterms']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Date Added=-->
					<td data-order="<?php echo $quotation['customs-clearance']; ?>">
						<span class="fw-bolder">
							<?php echo $quotation['customs-clearance']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Date Added=-->
					<td data-order="<?php echo $quotation['insurance']; ?>">
						<span class="fw-bolder">
							<?php echo $quotation['insurance']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Date Added=-->
					<td data-order="<?php echo $quotation['pick_up_date']; ?>">
						<span class="fw-bolder">
							<?php echo $quotation['pick_up_date']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Date Added=-->
					<td data-order="<?php echo $quotation['created_at']; ?>">
						<span class="fw-bolder">
							<?php echo $quotation['created_at']; ?>
						</span>
					</td>
					<!--end::Date Added=-->
					<!--begin::Status=-->
					<td data-order="<?php echo $quotation['ordered']; ?>">
						<!--begin::Badges-->
						<div class="badge badge-light-<?php if ( $quotation['ordered'] == 'Completed' ) { echo 'success'; } else { echo 'warning'; } ?>">
							<?php echo $quotation['ordered']; ?>
						</div>
						<!--end::Badges-->
					</td>
					<!--end::Status=-->
				</tr>
				<!--end::Table row-->
				<?php
			}
			?>
			</tbody>
			<!--end::Table body-->
		</table>
		<!--end::Table-->
	</div>
	<!--end::Card body-->
</div>