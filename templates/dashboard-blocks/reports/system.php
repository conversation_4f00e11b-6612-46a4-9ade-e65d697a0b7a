<?php
use Wayz\Reports\System_Report;
use Wayz\Utils;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

$header        = Utils::get_system_report_header();
$system_report = new System_Report();
$offers_report = $system_report->generate_system_report_data();
?>
<div id="quotationsReport" class="card card-flush">
	<!--begin::Card header-->
	<div class="card-header align-items-center py-5 gap-2 gap-md-5">
		<!--begin::Card toolbar-->
		<div class="card-toolbar flex-row-fluid justify-content-end gap-5">
			<a href="?export_data=export" id="reportExport" class="btn btn-primary">Export</a>
		</div>
		<!--end::Card toolbar-->
	</div>
	<!--end::Card header-->
	<!--begin::Card body-->
	<div class="card-body table-responsive pt-0">
		<!--begin::Table-->
		<table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_sales_table">
			<!--begin::Table head-->
			<thead>
			<!--begin::Table row-->
			<tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
				<?php
				foreach ( $header as $th_title ) {
					echo '<th class="min-w-100px">' . $th_title . '</th>';
				}
				?>
			</tr>
			<!--end::Table row-->
			</thead>
			<!--end::Table head-->
			<!--begin::Table body-->
			<tbody class="fw-bold text-gray-600">
			<?php
			foreach ( $offers_report as $offer ) {
				echo '<tr>';
				$offer_row = array();
				foreach ( $header as $key => $title ) {
					if ( isset( $offer[ $key ] ) ) {
						echo '<td>' . $offer[ $key ] . '</td>';
					} else {
						echo '<td>-</td>';
					}
				}
				echo '</tr>';
			}
			?>
			</tbody>
			<!--end::Table body-->
		</table>
		<!--end::Table-->
	</div>
	<!--end::Card body-->
</div>
