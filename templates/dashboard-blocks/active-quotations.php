<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $overview ) ) {
	$overview = false;
}

if ( empty( $user_id ) ) {
	$user_id = get_current_user_id();

	if ( current_user_can( 'manage_options' ) ) {
		$overview = true;
	}
}

use Wayz\Controllers\Quotations;
?>
<!--begin::Active Quotations block-->
<div id="activeQuotationsSection" class="card h-md-100">
	<!--begin::Header-->
	<div class="card-header align-items-center border-0">
		<!--begin::Title-->
		<h3 class="fw-bolder text-gray-900 m-0">Active Quotations</h3>
		<!--end::Title-->
		<button class="btn btn-icon btn-color-gray-400 btn-active-color-primary justify-content-end"
		        data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end" data-kt-menu-overflow="true">
			<!--begin::Svg Icon | path: icons/duotune/general/gen023.svg-->
			<span class="svg-icon svg-icon-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.3" x="2" y="2" width="20" height="20" rx="4" fill="currentColor"></rect>
                        <rect x="11" y="11" width="2.6" height="2.6" rx="1.3" fill="currentColor"></rect>
                        <rect x="15" y="11" width="2.6" height="2.6" rx="1.3" fill="currentColor"></rect>
                        <rect x="7" y="11" width="2.6" height="2.6" rx="1.3" fill="currentColor"></rect>
                    </svg>
                </span>
			<!--end::Svg Icon-->
		</button>
	</div>
	<!--end::Header-->
	<!--begin::Body-->
	<div class="card-body pt-2">
		<!--begin::Nav-->
		<!--end::Nav-->
		<!--begin::Tab Content-->
		<div class="tab-content">
			<!--begin::Tap pane-->
			<div class="tab-pane fade show active" id="kt_stats_widget_2_tab_1">
				<?php
				$activeQuotations = ( new Quotations )->getActiveQuotations( $user_id, false, $overview );
				/*
				 * Need to refactor
				 */
				foreach ( $activeQuotations as $quotation ) {
					?>
					<!--begin::Shipment block-->
					<div class="stepper stepper-pills stepper-column d-flex flex-row-auto mt-10 row">
						<div class="stepper-title mb-10 d-flex justify-content-between">
							<div>
								<h2 class="fs-1 fw-bolder text-black-700">
									<?php echo $quotation['title']; ?>
								</h2>
								<h3 class="fs-6 fw-bolder text-gray-700">
									ID <?php echo $quotation['id'] . '//' . $quotation['date'] ; ?>
								</h3>
								<span class="fs-7 fw-normal text-gray-400">
									<?php Quotations::generate_quotation_data( $quotation['id'] ); ?>
								</span>
							</div>
							<div>
								<a href="<?php echo get_page_url_by_template( 'templates/page-offers-list.php' ) . '?quotation_id=' . $quotation['id']; ?>">
									<span class="bg-success text-white rounded-2 p-3 float-end">
										<?php echo $quotation['offers_count']; ?> Offers
									</span>
								</a>
							</div>
						</div>
						<!--begin::Stepper-Order-->
						<div class="stepper bg-white d-flex justify-content-between mb-10">
							<!--begin::Nav-->
							<div class="stepper-nav justify-content-start">
								<!--begin::Step 1-->
								<div class="stepper-item me-5 current" data-kt-stepper-element="nav">
									<!--begin::Line-->
									<div class="stepper-line w-40px"></div>
									<!--end::Line-->
									<!--begin::Icon-->
									<div class="stepper-icon w-40px h-40px">
										<i class="stepper-check fas fa-check"></i>
										<span class="stepper-number">1</span>
									</div>
									<!--end::Icon-->

									<!--begin::Label-->
									<div class="stepper-label">
										<h3 class="stepper-title">
											<?php echo $quotation['origin_port']; ?>
										</h3>
										<div class="stepper-desc text-break w-200px">
											<?php echo $quotation['pickup_address']; ?>
										</div>
									</div>
									<!--end::Label-->
								</div>
								<!--end::Step 1-->

								<!--begin::Step 2-->
								<div class="stepper-item me-5" data-kt-stepper-element="nav">
									<!--begin::Line-->
									<div class="stepper-line w-40px"></div>
									<!--end::Line-->

									<!--begin::Icon-->
									<div class="stepper-icon w-40px h-40px">
										<i class="stepper-check fas fa-check"></i>
										<span class="stepper-number">2</span>
									</div>
									<!--begin::Icon-->

									<!--begin::Label-->
									<div class="stepper-label">
										<h3 class="stepper-title text-break w-200px">
											<?php echo $quotation['destination_location']; ?>
										</h3>
									</div>
									<!--end::Label-->
								</div>
								<!--end::Step 2-->
							</div>
							<!--end::Nav-->
							<div>
								<div class="mb-5">
									<div class="flex-column current" data-kt-stepper-element="content">
										<div class="fv-row ">
											<p>
												<span class="fs-5 fw-bold">Fright Forwarder Emails: </span>
												<br>
												<?php
												foreach ( $quotation['fright_companies'] as $company_email ) {
													$user = get_user_by( 'email', $company_email );
													if ( $user ) {
														echo '<i class="bi bi-building fs-1x success"></i>  ' . $user->display_name . "<br>";
													}
												}
												?>
											</p>
										</div>
										<div class="fv-row">
											<p>
												<span class="fs-5 fw-bold">Incoterms: </span><?php echo $quotation['incoterms']; ?>
											</p>
										</div>
									</div>
								</div>

							</div>
						</div>
						<!--end::Stepper-Order-->
					</div>
					<!--end::Shipment block-->
					<?php
				}
				if ( empty( $activeQuotations ) ) {
					wc_get_template(
						'dashboard-blocks/404.php',
						[ 'error' => 'There is no Active Shipments' ],
						'',
						get_template_directory() . '/templates/'
					);
				}
				?>
			</div>
			<!--end::Tap pane-->
		</div>
		<!--end::Tab Content-->
	</div>
	<!--end: Card Body-->
</div>
<!--end::Active Quotations block-->
