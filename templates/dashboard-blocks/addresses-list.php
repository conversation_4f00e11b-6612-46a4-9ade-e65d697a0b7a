<?php

use <PERSON>z\Controllers\Addresses;
use Wayz\Utils;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( empty( $user_id ) ) {
	return;
}
?>
<!--begin::Table-->
<div id="userAddressesList" class="dataTables_wrapper dt-bootstrap4 no-footer">
	<div class="table-responsive">
		<table class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
			<!--begin::Table head-->
			<thead>
			<!--begin::Table row-->
			<tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 167.333px;">Country</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 167.333px;">State</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 167.333px;">City</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 207.267px;">Address 1</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 167.333px;">Phone number</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 218.933px;">Email Address</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 218.933px;">Edit</th>
			</tr>
			<!--end::Table row-->
			</thead>
			<!--end::Table head-->
			<!--begin::Table body-->
			<tbody class="fw-semibold text-gray-600">
			<?php
			$edit_address_link = get_page_url_by_template( 'templates/page-add-address.php' );
			/**
			 * todo: find a way to get multiple user addresses
			 */
			$addresses = ( new Addresses )->list_with_ids( get_current_user_id() );
			foreach ( $addresses as $address ) {
				?>
				<tr class="odd">
					<td>
						<a href="<?php echo $edit_address_link . '/?id=' . $address['id'];?>">
							<?php echo $address['country']; ?>
						</a>
					</td>
					<td>
						<?php echo $address['state']; ?>
					</td>
					<td>
						<?php echo $address['city']; ?>
					</td>
					<td>
						<?php echo Utils::getFormattedAddress( $address['address1'] ); ?>
					</td>
					<td>
						<?php echo $address['phone_number']; ?>
					</td>
					<td>
						<?php echo $address['email_address']; ?>
					</td>
					<td>
						<a href="<?php echo $edit_address_link . '?address_id=' . $address['id'];?>" class="btn btn-primary fs-8">Edit</a>
					</td>
				</tr>
				<?php
			}
			?>
			</tbody>
			<!--end::Table body-->
		</table>
	</div>
</div>
<!--end::Table-->
