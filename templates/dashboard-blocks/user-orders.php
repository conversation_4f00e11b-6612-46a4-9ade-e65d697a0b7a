<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( empty( $user_id ) ) {
	return;
}

use Wayz\Controllers\Orders;
use Wayz\Controllers\Quotations;

?>
<!--begin::Container-->
<div id="userOrders">
	<?php
	$current_page = intval( ( $_GET['c_paged'] ?? 1 ) );
	$orders = ( new Orders() )->getUserOrders( $user_id, $current_page );

	foreach ( $orders['orders'] as $order ) {
		?>
		<!--begin::Stepper-->
		<div class="stepper stepper-pills stepper-column d-flex flex-column flex-lg-row bg-white p-15 mb-10">
			<!--begin::Aside-->
			<div class="d-flex flex-row-auto w-100 w-lg-300px">
				<!--begin::Nav-->
				<div class="stepper-nav">
					<div class="stepper-title mb-10">
						<h1 class="fs-1 fw-bolder text-black-700">
							<a href="<?php echo get_page_url_by_template( 'templates/page-order-details.php' ) . '?order_id=' . $order['id']; ?>">
								<?php echo $order['title']; ?>
							</a>
						</h1>
						<h2 class="fs-6 fw-bolder text-gray-700">ID <?php echo $order['id'] . ' // ' .  $order['created_at']; ?></h2>
						<span class="fs-7 fw-normal text-gray-400">
							<?php Quotations::generate_quotation_data( $order['quotation']['id'] ); ?>
						</span>
						<!--end::Stepper Title-->
					</div>
					<!--begin::Step 1-->
					<div class="stepper-item me-5 current" data-kt-stepper-element="nav">
						<!--begin::Line-->
						<div class="stepper-line w-40px">
							<!--begin::Stepper Title-->
							<!--end::Stepper Title-->
						</div>
						<!--end::Line-->

						<!--begin::Icon-->
						<div class="stepper-icon w-40px h-40px">
							<span class="stepper-number">
								<i class="icon-xl la la-map text-white fs-2x text-center p-4"></i>
							</span>
						</div>
						<!--end::Icon-->

						<!--begin::Label-->
						<div class="stepper-label">
							<h3 class="stepper-title text-black">
								<?php _e( 'Origin warehuse', 'wayz' ); ?>
							</h3>
							<div class="stepper-desc">
								-
							</div>
						</div>
						<!--end::Label-->
					</div>
					<!--end::Step 1-->

					<!--begin::Latest Step-->
					<div class="stepper-item me-5 <?php if ( $order['shipment_status'] == 'to-origin-port' ) { echo 'current'; } ?>" data-kt-stepper-element="nav">
						<!--begin::Line-->
						<div class="stepper-line w-40px"></div>
						<!--end::Line-->

						<!--begin::Icon-->
						<div class="stepper-icon w-40px h-40px">
							<i class="stepper-check fas fa-check"></i>
							<span class="stepper-number">
								<i class="icon-xl la la-truck text-primary fs-2x text-center p-4"></i>
							</span>
						</div>
						<!--begin::Icon-->

						<!--begin::Label-->
						<div class="stepper-label">
							<h3 class="stepper-title <?php if ( $order['shipment_status'] == 'to-origin-port' ) { echo 'text-black'; } ?>">
								<?php _e( 'On the way to origin port', 'wayz' ); ?>
							</h3>
							<div class="stepper-desc">
								<?php echo $order['quotation']['origin_port']; ?>
							</div>
						</div>
						<!--end::Label-->
					</div>
					<!--end::Latest Step-->

					<!--begin::Latest Step-->
					<div class="stepper-item me-5 <?php if ( $order['shipment_status'] == 'at-origin-port' ) { echo 'current'; } ?>" data-kt-stepper-element="nav">
						<!--begin::Line-->
						<div class="stepper-line w-40px"></div>
						<!--end::Line-->

						<!--begin::Icon-->
						<div class="stepper-icon w-40px h-40px">
							<i class="stepper-check fas fa-check"></i>
							<span class="stepper-number">
								<i class="icon-xl la la-map-pin text-primary fs-2x text-center p-4"></i>
							</span>
						</div>
						<!--begin::Icon-->

						<!--begin::Label-->
						<div class="stepper-label">
							<h3 class="stepper-title <?php if ( $order['shipment_status'] == 'at-origin-port' ) { echo 'text-black'; } ?>">
								<?php _e( 'Arrived at origin port', 'wayz' ); ?>
							</h3>
							<div class="stepper-desc">
								<?php echo $order['quotation']['origin_port']; ?>
							</div>
						</div>
						<!--end::Label-->
					</div>
					<!--end::Latest Step-->

					<!--begin::Latest Step-->
					<div class="stepper-item me-5 <?php if ( $order['shipment_status'] == 'in-transit' ) { echo 'current'; } ?>" data-kt-stepper-element="nav">
						<!--begin::Line-->
						<div class="stepper-line w-40px"></div>
						<!--end::Line-->

						<!--begin::Icon-->
						<div class="stepper-icon w-40px h-40px">
							<i class="stepper-check fas fa-check"></i>
							<span class="stepper-number">
								<i class="icon-xl la la-ship text-primary fs-2x text-center p-4"></i>
							</span>
						</div>
						<!--begin::Icon-->

						<!--begin::Label-->
						<div class="stepper-label">
							<h3 class="stepper-title <?php if ( $order['shipment_status'] == 'in-transit' ) { echo 'text-black'; } ?>">
								<?php _e( 'In transit', 'wayz' ); ?>
							</h3>
							<div class="stepper-desc">
								-
							</div>
						</div>
						<!--end::Label-->
					</div>
					<!--end::Latest Step-->

					<!--begin::Latest Step-->
					<div class="stepper-item me-5 <?php if ( $order['shipment_status'] == 'at-destination-port' ) { echo 'current'; } ?>" data-kt-stepper-element="nav">
						<!--begin::Line-->
						<div class="stepper-line w-40px"></div>
						<!--end::Line-->

						<!--begin::Icon-->
						<div class="stepper-icon w-40px h-40px">
							<i class="stepper-check fas fa-check"></i>
							<span class="stepper-number">
								<i class="icon-xl la la-map-pin text-primary fs-2x text-center p-4"></i>
							</span>
						</div>
						<!--begin::Icon-->

						<!--begin::Label-->
						<div class="stepper-label">
							<h3 class="stepper-title <?php if ( $order['status'] == 'completed' ) { echo 'text-black'; } ?>">
								<?php _e( 'Arrived at destination port', 'wayz' ); ?>
							</h3>
							<div class="stepper-desc">
								<?php echo $order['quotation']['destination_location']; ?>
							</div>
						</div>
						<!--end::Label-->
					</div>
					<!--end::Latest Step-->
				</div>
				<!--end::Nav-->
			</div>

			<!--begin::Content-->
			<div class="flex-row-fluid">
				<!--begin::Form-->
				<form class="form w-lg-500px mx-auto" novalidate="novalidate">
					<!--begin::Group-->
					<div class="mb-5">
						<!--begin::Step 1-->
						<div class="flex-column current d-flex mt-10" data-kt-stepper-element="content">
							<div class="justify-content-end">
								<span class="bg-success text-white rounded-2 p-3 float-end">
									<?php echo $order['status']; ?>
								</span>
							</div>
							<p>
								<span class="fs-5 fw-bold">Service Provider: </span>
								<?php echo $order['provider']; ?>
							</p>
							<p>
								<span class="fs-5 fw-bold">Order total: </span>
								<?php echo $order['original_price'] . ' ' . $order['original_currency']; ?>
							</p>
							<?php
							$fields = array(
								'incoterms'            => __( 'Incoterms', 'wayz' ),
								'origin_country'       => __( ' Origin Country ', 'wayz' ),
								'insurance'            => __( 'Insurance', 'wayz' ),
								'customs-clearance'    => __( 'Customs Clearance', 'wayz' ),
								'destination_location' => __( 'Destination location', 'wayz' )
							);
							foreach ( $fields as $key => $value ) {
								if ( $order['quotation'][ $key ] == '' ) {
									continue;
								}
								?>
								<p>
									<span class="fs-5 fw-bold"><?php echo $value; ?>: </span>
									<?php echo $order['quotation'][ $key ];?>
								</p>
								<?php
							}
							?>
						</div>
					</div>
					<!--end::Group-->

				</form>
				<!--end::Form-->
			</div>
		</div>
		<!--end::Stepper-->
		<?php
	}

	if ( empty( $orders['orders'] ) ) {
		?>
		<div class="mb-2">
			<!--begin::Title-->
			<h2 class="fw-bold text-gray-400 text-center lh-lg">
				There is no orders
			</h2>
			<!--end::Title-->
			<!--begin::Illustration-->
			<div class="flex-grow-1 bgi-no-repeat bgi-size-contain bgi-position-x-center card-rounded-bottom h-200px mh-200px my-5 my-lg-12" style="background-image:url('<?php echo get_template_directory_uri() . '/'; ?>assets/media/svg/illustrations/easy/2.svg')"></div>
			<!--end::Illustration-->
		</div>
		<?php
	} else {
		global $wp;
		$current_slug = add_query_arg( array(), $wp->request );
		?>
		<ul class="pagination">
			<?php
			if ( 1 < $current_page ) {
				?>
				<li class="page-item previous">
					<a href="<?php echo add_query_arg( array( 'c_paged' => $current_page - 1, ), $current_slug ); ?>" class="page-link">
						<i class="previous"></i>
						<?php _e( 'Previous', 'wayz' ); ?>
					</a>
				</li>
				<?php
			}
			if ( $orders['max_num_pages'] > $current_page ) {
				?>
				<li class="page-item next">
					<a href="<?php echo add_query_arg( array( 'c_paged' => $current_page + 1, ), $current_slug ); ?>" class="page-link">
						<?php _e( 'Next', 'wayz' ); ?>
						<i class="next"></i>
					</a>
				</li>
				<?php
			}
			?>
		</ul>
		<?php
	}
	?>
</div>
<!--end::Container-->
