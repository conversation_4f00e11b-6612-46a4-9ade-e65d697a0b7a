<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $overview ) ) {
	$overview = false;
}

if ( empty( $user_id ) ) {
	$user_id = get_current_user_id();

	if ( current_user_can( 'manage_options' ) ) {
		$overview = true;
	}
}

use <PERSON>z\Controllers\Offers;
use Wayz\Controllers\Quotations;
?>
<!--begin::Latest offers block-->
<div id="latestOffersSection" class="card h-md-100">
	<!--begin::Header-->
	<div class="card-header align-items-center border-0">
		<!--begin::Title-->
		<h3 class="fw-bolder text-gray-900 m-0">Latest offers</h3>
		<!--end::Title-->
	</div>
	<!--end::Header-->
	<!--begin::Body-->
	<div class="card-body pt-2">
		<!--begin::Tab Content-->
		<div class="tab-content">
			<!--begin::Tap pane-->
			<div class="tab-pane fade show active" id="kt_stats_widget_2_tab_1">
				<!--begin::Table container-->
				<div class="table-responsive">
					<!--begin::Table-->
					<table class="table align-middle m-0">
						<tbody>
						<?php
						$comments = ( new Offers() )->getCustomerActiveOffers( $user_id, $overview );

						if ( empty( $comments ) ) {
							wc_get_template(
								'dashboard-blocks/404.php',
								[ 'error' => 'There is no New Offers' ],
								'',
								get_template_directory() . '/templates/'
							);
						}

						foreach ( $comments as $comment ) {
							?>
							<tr>
								<td>
									<div class="d-flex align-items-center">
										<div class="d-flex justify-content-start flex-column align-items-center">
											<span
												class="fw-bolder text-hover-primary fs-2"><?php echo get_comment_meta( $comment->comment_ID, 'transit_days', true ); ?></span>
											<span class="fw-bold d-block fs-5">DAYS</span>
											<span class="text-gray-400 fs-7">TRANSIT TIME</span>
										</div>
									</div>
								</td>
								<td class="pe-0">
									<span class="fw-bolder fs-3">
										<?php echo get_the_title( $comment->comment_post_ID ); ?>
									</span>
								</td>
								<td class="text-end pe-0">
									<span class="fw-bolder fs-4">
										<?php echo $comment->comment_author; ?>
									</span>
								</td>
								<td class="text-end pe-12">
									<span class="fw-bolder fs-4">
										<?php echo get_comment_meta( $comment->comment_ID, 'offer_price', true ) . ' ' . get_comment_meta( $comment->comment_ID, 'offer_currency', true ); ?>
									</span>
								</td>
								<td class="text-end">
									<a href="#" class="btn btn-sm btn-secondary text-hover-black text-black text-center fw-normal m-1" data-bs-toggle="modal" data-bs-target="#offer_<?php echo $comment->comment_ID; ?>">
										See details
									</a>
									<?php
									$approve_link = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]&approve=approve&offer_id=" . $comment->comment_ID;
									?>
									<a href="<?php echo get_page_url_by_template( 'templates/page-offers-list.php' ) . '?quotation_id=' . $comment->comment_post_ID; ?>" class="btn btn-sm btn-primary btn-active-light-primary m-1">
										Quotation page
									</a>
									<div class="modal fade" id="offer_<?php echo $comment->comment_ID; ?>" tabindex="-1" style="display: none;" aria-hidden="true">
										<div class="modal-dialog modal-xl">
											<div class="modal-content rounded">
												<div class="modal-header justify-content-end border-0 pb-0">
													<div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                                                        <span class="svg-icon svg-icon-1">
                                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor"></rect>
                                                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor"></rect>
                                                            </svg>
                                                        </span>
													</div>
												</div>
												<div class="modal-body pt-0 pb-10 px-5 px-xl-10">
													<div class="mb-8 text-center">
														<h1 class="mb-1">Offer Details</h1>
													</div>
													<div>
														<p>
															<?php echo $comment->comment_content; ?>
														</p>
													</div>
												</div>
											</div>
										</div>
									</div>
								</td>
							</tr>
							<?php
						}
						?>
						</tbody>
					</table>
					<!--end::Table-->
				</div>
			</div>
			<!--end::Tap pane-->
		</div>
		<!--end::Tab Content-->
	</div>
	<!--end: Card Body-->
</div>
<!--end::Latest offers block-->