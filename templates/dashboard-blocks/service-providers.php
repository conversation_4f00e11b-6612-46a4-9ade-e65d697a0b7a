<?php

use Wayz\Controllers\ServiceProvider;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( empty( $user_id ) ) {
	return;
}
?>
<!--begin::Table-->
<div id="serviceProvidersList" class="dataTables_wrapper dt-bootstrap4 no-footer">
	<div class="table-responsive">
		<table class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer">
			<!--begin::Table head-->
			<thead>
			<!--begin::Table row-->
			<tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 167.333px;">Company Name</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 167.333px;">Email</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 207.267px;">Phone</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 167.333px;">Contact</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 218.933px;">Country</th>
				<th class="min-w-125px sorting" tabindex="0" rowspan="1" colspan="1" style="width: 218.933px;">Add contact</th>
			</tr>
			<!--end::Table row-->
			</thead>
			<!--end::Table head-->
			<!--begin::Table body-->
			<tbody class="fw-semibold text-gray-600">
			<?php
			$companies = ( new ServiceProvider () )->migrate_old_data( (int) $user_id );
			$contact_link = get_page_url_by_template( 'templates/page-add-sp-contact.php' );

			foreach ( $companies as $company ) {
				$company_profile = get_user_by( 'ID', $company['id'] );
				?>
				<tr class="odd">
					<td>
						<a href="<?php echo get_page_url_by_template('templates/page-add-company.php') . '/?edit_company='.$company['id'];?>">
							<?php echo $company['name']; ?>
						</a>
					</td>

					<td>
						<?php echo $company_profile->user_email; ?>
					</td>

					<td>
						<?php echo get_user_meta( $company_profile->ID, 'phone', true ); ?>
					</td>

					<td>
						<?php echo get_user_meta( $company_profile->ID, 'contact', true ); ?>
					</td>

					<td>
						<?php echo get_user_meta( $company_profile->ID, 'country', true ); ?>
					</td>

					<td>
						<a href="<?php echo $contact_link.'?sp_id='.$company_profile->ID;?>" class="btn btn-primary fs-8">Add contact</a>
					</td>
				</tr>
				<?php
			}
			?>
			</tbody>
			<!--end::Table body-->
		</table>
	</div>
</div>
<!--end::Table-->
