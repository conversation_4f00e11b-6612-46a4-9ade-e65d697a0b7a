<?php

use <PERSON>z\Controllers\Quotations;
use Wayz\Permissions\Quotations_Offers;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( empty( $user_id ) ) {
	return;
}
?>
<!--begin::Container-->
<div id="userQuotations">
	<?php
	$current_page = intval( ( $_GET['c_paged'] ?? 1 ) );
	$args         = array(
		'author'         => $user_id,
		'orderby'        => 'post_date',
		'order'          => 'DSC',
		'post_type'      => 'quotations',
		'posts_per_page' => 12,
		'post_status'    => array( 'publish', 'draft' ),
		'paged'          => $current_page,
		'paginate'       => true,
	);
	$Quotations   = new WP_Query( $args );
	foreach ( $Quotations->posts as $Quotation ) {
		$order_id                       = get_post_meta( $Quotation->ID, '_order_id', true );
		$is_quotation_receive_offers    = Quotations_Offers::is_quotation_receive_offers( $Quotation->ID );
		$quotation_receive_offers_until = Quotations_Offers::get_quotation_receive_offers_until( $Quotation->ID );
		?>
		<!--begin::Stepper-->
		<div class="position-relative stepper stepper-pills stepper-column d-flex flex-column flex-lg-row bg-white p-15 mb-10">
			<?php
			if ( $is_quotation_receive_offers ) {
				?>
				<span class="position-absolute top-0 badge badge-success badge-lg mb-3">Accept offers ( <?php echo $quotation_receive_offers_until; ?> )</span>
				<?php
			} else {
				?>
				<span class="position-absolute top-0 badge badge-danger badge-lg mb-3">Offering closed</span>
				<?php
			}
			?>
			<!--begin::Aside-->
			<div class="d-flex flex-row-auto w-100 w-lg-300px">
				<!--begin::Nav-->
				<div class="stepper-nav">
					<div class="stepper-title mb-10">
						<h2 class="fs-1 fw-bolder text-black-700"><?php echo $Quotation->post_title; ?></h2>
						<a href="<?php echo get_page_url_by_template( 'templates/page-offers-list.php' ) . '?quotation_id=' . $Quotation->ID; ?>" class="fs-6 fw-bolder text-gray-700 text-hover-gray-900 ">ID <?php echo $Quotation->ID; ?> // <?php echo $Quotation->post_date; ?></a>
						<p class="fs-7 fw-normal text-gray-400">
							<?php Quotations::generate_quotation_data( $Quotation->ID ); ?>
						</p>
					</div>
					<!--begin::Step 1-->
					<div class="stepper-item me-5 current" data-kt-stepper-element="nav">
						<!--begin::Line-->
						<div class="stepper-line w-40px">
							<!--begin::Stepper Title-->
							<!--end::Stepper Title-->
						</div>
						<!--end::Line-->

						<!--begin::Icon-->
						<div class="stepper-icon w-40px h-40px">
                                    <span class="stepper-number">
                                        <i class="icon-xl la la-map text-white fs-2x text-center p-4"></i>
                                    </span>
						</div>
						<!--end::Icon-->

						<!--begin::Label-->
						<div class="stepper-label">
							<h3 class="stepper-title">
								<?php echo get_post_meta( $Quotation->ID, '_origin_port', true ); ?>
							</h3>
							<div class="stepper-desc">
								<?php echo get_post_meta( $Quotation->ID, '_pick_up_date', true ); ?>
							</div>
						</div>
						<!--end::Label-->
					</div>
					<!--end::Step 1-->
					<!--begin::Step 2-->
					<div class="stepper-item me-5" data-kt-stepper-element="nav">
						<!--begin::Line-->
						<div class="stepper-line w-40px"></div>
						<!--end::Line-->

						<!--begin::Icon-->
						<div class="stepper-icon w-40px h-40px">
							<i class="stepper-check fas fa-check"></i>
							<span class="stepper-number"><i class="icon-xl la la-map text-primary fs-2x text-center p-4"></i></span>
						</div>
						<!--begin::Icon-->

						<!--begin::Label-->
						<div class="stepper-label">
							<h3 class="stepper-title">
								<?php
								$address = get_post_meta( $Quotation->ID, '_destination_location', true );
								if( strpos( $address, 'MAP::' ) !== false ) {
									$map = str_replace('MAP::','', $address);
									$address = 'https://www.google.com/maps/search/'.$map;
								}
								if ( strpos( $address, "http://" ) !== false || strpos( $address, "https://" ) !== false ) {
									echo '<a href="'. $address .'" target="_blank"><i class="bi bi-pin-map fs-2x text-primary"></i></a>';
								} else {
									echo $address;
								}
								?>
							</h3>
							<div class="stepper-desc">
								-
							</div>
						</div>
						<!--end::Label-->
					</div>
					<!--end::Step 2-->
				</div>
				<!--end::Nav-->
			</div>

			<!--begin::Content-->
			<div class="flex-row-fluid">
				<!--begin::Form-->
				<span class="<?php if ( 'draft' === $Quotation->post_status ) {
					echo 'btn-info';
				} else {
					echo 'bg-success';
				} ?> text-white rounded-2  p-3 float-end m-3">
                            <?php
                            if ( 'draft' === $Quotation->post_status ) {
	                            ?>
	                            <a href="<?php echo get_page_url_by_template( 'templates/page-get-quotations.php' ) . '?action=edit&quotation_id=' . $Quotation->ID; ?>" class="text-white">Edit</a>
	                            <?php
                            } else {
	                            ?>
	                            <a href="<?php echo get_page_url_by_template( 'templates/page-offers-list.php' ) . '?quotation_id=' . $Quotation->ID; ?>" class="text-white">View Offers</a>
	                            <?php
                            }
                            ?>
						</span>
				<span class="bg-light text-white rounded-2  p-3 float-end m-3">
                            <a href="<?php echo get_page_url_by_template( 'templates/page-get-quotations.php' ) . '?action=duplicate&quotation_id=' . $Quotation->ID; ?>">Duplicate</a>
						</span>
				<?php
				if ( $order_id != '' ) {
					?>
					<span class="bg-primary text-white rounded-2  p-3 float-end m-3">
                                <a href="<?php echo get_page_url_by_template( 'templates/page-order-details.php' ) . '?order_id=' . $order_id; ?>" class="text-white">View Order</a>
                            </span>
					<?php
				}
				?>
				<form class="form w-lg-500px mx-auto" novalidate="novalidate">
					<!--begin::Group-->
					<div class="mb-5">
						<!--begin::Step 1-->
						<div class="flex-column current d-flex mt-10" data-kt-stepper-element="content">
							<?php
							$fields = array(
								'_shipment_type'     => __( 'Shipment type', 'wayz' ),
								'_load_type'         => __( 'Load type', 'wayz' ),
								'_incoterms'         => __( 'Incoterms', 'wayz' ),
								'_origin_country'    => __( ' Origin Country ', 'wayz' ),
								'_insurance'         => __( 'Insurance', 'wayz' ),
								'_customs-clearance' => __( 'Customs Clearance', 'wayz' ),
								'_destination_location' => __( 'Destination location', 'wayz' )
							);
							foreach ( $fields as $key => $value ) {
								$result = get_post_meta( $Quotation->ID, $key, true );
								if ( $result == '' ) {
									continue;
								}
								?>
								<p>
									<span class="fs-5 fw-bold"><?php echo $value; ?>: </span>
									<?php
									if( strpos( $result, 'MAP::' ) !== false ) {
										$map = str_replace('MAP::','', $result);
										$result = 'https://www.google.com/maps/search/'.$map;
									}
									if ( strpos( $result, "http://" ) !== false || strpos( $result, "https://" ) !== false ) {
										echo '<a href="'. $result .'" target="_blank"><i class="bi bi-pin-map fs-2x text-primary"></i></a>';
									} else {
										echo $result;
									}
									?>
								</p>
								<?php
							}
							?>
						</div>
					</div>
					<!--end::Group-->

				</form>
				<!--end::Form-->
			</div>

		</div>
		<!--end::Stepper-->
		<?php
	}

	if ( empty( $Quotations->posts ) ) {
		?>
		<div class="mb-2">
			<!--begin::Title-->
			<h2 class="fw-bold text-gray-400 text-center lh-lg">
				There is no active Quotations
			</h2>
			<!--end::Title-->
			<!--begin::Illustration-->
			<div class="flex-grow-1 bgi-no-repeat bgi-size-contain bgi-position-x-center card-rounded-bottom h-200px mh-200px my-5 my-lg-12" style="background-image:url('<?php echo get_template_directory_uri() . '/'; ?>assets/media/svg/illustrations/easy/2.svg')"></div>
			<!--end::Illustration-->
		</div>
		<?php
	} else {
		global $wp;
		$current_slug = add_query_arg( array(), $wp->request );
		?>
		<ul class="pagination">
			<?php
			if ( 1 < $current_page ) {
				?>
				<li class="page-item previous">
					<a href="<?php echo add_query_arg( array( 'c_paged' => $current_page - 1, ), $current_slug ); ?>" class="page-link"><i class="previous"></i><?php _e( 'Previous', 'wayz' ); ?></a></li>
				<?php
			}
			if ( $Quotations->max_num_pages > $current_page ) {
				?>
				<li class="page-item next">
					<a href="<?php echo add_query_arg( array( 'c_paged' => $current_page + 1, ), $current_slug ); ?>" class="page-link"><?php _e( 'Next', 'wayz' ); ?><i class="next"></i></a></li>
				<?php
			}
			?>
		</ul>
		<?php
	}
	?>
</div>
<!--end::Container-->
