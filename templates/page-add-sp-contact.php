]<?php
/**
 * Template Name: Add new contact
 */

use Wayz\Controllers\ServiceProvider;

defined( 'ABSPATH' ) || exit;
get_header( '' );

$user_id = get_current_user_id();

if ( isset( $_GET['add_new_contact'] ) ) {
	$contact = array(
		'company_id' => intval( $_GET['sp_id'] ),
		'name'       => sanitize_text_field( $_GET['contact_name'] ),
		'phone'      => sanitize_text_field( $_GET['contact_phone'] ),
		'email'      => sanitize_email( $_GET['contact_email'] ),
	);

	$meta = ( new ServiceProvider() )->add_contact( $contact );
}
?>
<!--begin::Post-->
<?php
if ( ! isset( $_GET['add_new_contact'] ) || ! $meta ) {
	?>
	<!--begin::Container-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<!--begin::Layout-->
			<div class="d-flex flex-column flex-lg-row">
				<!--begin::Content-->
				<div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
					<!--begin::Card-->
					<div class="card">
						<!--begin::Card body-->
						<div class="card-body p-12">
							<!--begin::Form-->
							<form action="" method="get" id="add_new_contact">
								<input hidden style="display:none;" name="sp_id" value="<?php echo intval( $_GET['sp_id'] ) ?>" type="text">
								<div class="row">
									<div class="col-xl-6">
										<label class="form-label fs-3 fw-bolder text-gray-900 mb-6">Add new contact</label>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700">Name</label>
											<input name="contact_name" required type="text" class="form-control form-control-solid" value="<?php echo $_GET['contact_name'] ?? '';?>">
										</div>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700">Phone Number</label>
											<input name="contact_phone" required type="text" class="form-control form-control-solid" value="<?php echo $_GET['contact_phone'] ?? '';?>">
										</div>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700">Email</label>
											<input name="contact_email" required type="email" class="form-control form-control-solid" value="<?php echo $_GET['contact_email'] ?? '';?>">
										</div>
										<?php
										if ( isset( $meta ) && ! $meta ) {
											?>
											<div class="d-flex flex-column">
												<h4 class="mb-1 text-danger">Email Exist</h4>
												<span>This email already exist!</span>
											</div>
											<?php
										}
										?>
									</div>
								</div>
								<div class="text-center">
									<input type="submit" name="add_new_contact" class="btn btn-primary" value="Save">
								</div>
							</form>
							<!--end::Form-->
						</div>
						<!--end::Card body-->
					</div>
					<!--end::Card-->
				</div>
				<!--end::Content-->
			</div>
			<!--end::Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Container-->
	<?php
}
?>
<!--end::Post-->
<?php
if ( isset( $_GET['add_new_contact'] ) && $meta ) {
	?>
	<div class="d-flex flex-center flex-column flex-column-fluid">
		<div class="w-lg-600px bg-body rounded shadow-sm mx-auto ">
			<div class="card card-custom bg-success">
				<div class="card-header justify-content-center">
					<div class="card-title">
						<h3 class="card-label text-white fw-bold fw-1">Contact added</h3>
					</div>
					<div class="card-body text-white text-center">
						<a href="<?php echo get_page_url_by_template('templates/page-service-providers-list.php');?>" class="btn btn-sm fw-bold btn-primary">Service Providers List</a>
					</div>
				</div>
			</div>
		</div>
	</div>
	<?php
}

get_footer();