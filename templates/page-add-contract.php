<?php
/**
 * Template Name: Add contract
 */

use <PERSON>z\Controllers\Contracts;
use Wayz\Locker\EditLock;

defined( 'ABSPATH' ) || exit;

$user_id = get_current_user_id();
$lock = false;

if( isset( $_POST['add_edit_contract'] ) && isset( $_POST['contract_nonce'] ) && wp_verify_nonce( $_POST['contract_nonce'], 'contract_nonce' ) ){
	$trips = array();
    foreach ( $_POST['contract_trips'] as $trip ){
        // string to array
        $trip_array = explode('||', $trip );
	    $trips[] = array(
            'origin' => $trip_array[0],
            'destination' => $trip_array[1],
            'cost' => $trip_array[2]
        );
    }

    $contract_data = [
	    'user_id' => $user_id,
	    'title' => $_POST['contract_title'],
	    'type' => $_POST['contract_type'],
	    'maximum_containers' => $_POST['contract_maximum_containers'],
	    'maximum_money' => $_POST['contract_maximum_money'],
	    'currency' => $_POST['currency'],
	    'end_date' => $_POST['contract_end_date'],
	    'company_email' => $_POST['contact_company'],
	    'trips' => $trips,
        'edit_cost' => $_POST['contract_edit_cost'] ?? '',
    ];

    if( isset( $_GET['contract_id'] ) && $_GET['contract_id'] != '' ){
        $contract_data['id'] = $_GET['contract_id'];
	    $contract_id = ( new Contracts() )->update( intval( $_GET['contract_id'] ), $contract_data );
    } else {
	    $contract_id = ( new Contracts() )->create( $contract_data );
    }

}

if( isset( $_GET['contract_id'] ) && $_GET['contract_id'] != '' && get_current_user_id() == get_post_field( 'post_author', $_GET['contract_id'] ) ){
	$contract       = ( new Contracts() )->get_contract( intval( $_GET['contract_id'] ) );
	$lock           = ( new EditLock() )->check( intval( $_GET['contract_id'] ) );
} elseif( isset( $_GET['contract_id'] ) && get_current_user_id() != get_post_field( 'post_author', $_GET['contract_id'] ) ){
	wp_redirect( get_bloginfo('url') );
} else {
    $contract = [];
}

get_header( '' );

?>
	<!--begin::Container-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
            <div class="d-flex flex-column gap-7 gap-lg-10">
                <?php
                if ( $lock ){
                    ?>
                    <div class="card card-flush w-lg-650px py-5 m-auto text-center">
                        <div class="card-body py-15 py-lg-20">
                            <!--begin::Title-->
                            <h1 class="fw-bolder fs-2hx text-gray-900 mb-4">Oops!</h1>
                            <!--end::Title-->
                            <!--begin::Text-->
                            <div class="fw-semibold fs-6 text-gray-500 mb-7">You cant edit this contract, Abdalsalaam currently editing.</div>
                            <!--end::Text-->
                            <!--begin::Illustration-->
                            <div class="mb-3">
                                <img src="<?php echo get_template_directory_uri().'/'; ?>assets/media/illustrations/dozzy-1/9.png" class="mw-100 mh-300px theme-light-show" alt="">
                            </div>
                            <!--end::Illustration-->
                            <!--begin::Link-->
                            <div class="mb-0">
                                <a href="<?php echo get_page_url_by_template('templates/page-user-contracts.php');?>" class="btn btn-sm btn-primary">Contracts list</a>
                            </div>
                            <!--end::Link-->
                        </div>
                    </div>
                    <?php
                } else {
                    ?>
                    <div class="d-flex flex-wrap flex-stack gap-5 gap-lg-10">
                        <!--begin:::Tabs-->
                        <ul class="nav nav-custom nav-tabs nav-line-tabs nav-line-tabs-2x border-0 fs-4 fw-bold mb-lg-n2 me-auto">
                            <!--begin:::Tab item-->
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4 active" data-bs-toggle="tab"
                                   href="#add_contract_tab">Contract Details</a>
                            </li>
                            <!--end:::Tab item-->
                            <!--begin:::Tab item-->
                            <li class="nav-item">
                                <a class="nav-link text-active-primary pb-4" data-bs-toggle="tab"
                                   href="#edit_history">Edit History</a>
                            </li>
                            <!--end:::Tab item-->
                        </ul>
                        <!--end:::Tabs-->
                    </div>
                    <!--begin::Tab content-->
                    <div class="tab-content">
                        <!--begin::Tab pane-->
                        <div class="tab-pane fade show active" id="add_contract_tab" role="tab-panel">
                            <!--begin::Card-->
                            <div class="card" id="add_contract_card">
                                <!--begin::Card body-->
                                <div class="card-body p-12">
                                    <!--begin::Stepper-->
                                    <div class="stepper stepper-pills stepper-column d-flex flex-column flex-lg-row" id="add_contract_stepper">
                                        <!--begin::Aside-->
                                        <div class="d-flex flex-row-auto w-100 w-lg-300px">
                                            <!--begin::Nav-->
                                            <div class="stepper-nav flex-cente">
                                                <!--begin::Step 1-->
                                                <div class="stepper-item me-5<?php if(!isset($contract_id)) echo ' current'; else echo ' completed'; ?>" data-kt-stepper-element="nav">
                                                    <!--begin::Wrapper-->
                                                    <div class="stepper-wrapper d-flex align-items-center">
                                                        <!--begin::Icon-->
                                                        <div class="stepper-icon w-40px h-40px">
                                                            <i class="stepper-check fas fa-check"></i>
                                                            <span class="stepper-number">1</span>
                                                        </div>
                                                        <!--end::Icon-->

                                                        <!--begin::Label-->
                                                        <div class="stepper-label">
                                                            <h3 class="stepper-title">
                                                                Step 1
                                                            </h3>
                                                            <div class="stepper-desc">
                                                                Company details
                                                            </div>
                                                        </div>
                                                        <!--end::Label-->
                                                    </div>
                                                    <!--end::Wrapper-->

                                                    <!--begin::Line-->
                                                    <div class="stepper-line h-40px"></div>
                                                    <!--end::Line-->
                                                </div>
                                                <!--end::Step 1-->

                                                <!--begin::Step 2-->
                                                <div class="stepper-item me-5<?php if(isset($contract_id)) echo ' completed'; ?>" data-kt-stepper-element="nav">
                                                    <!--begin::Wrapper-->
                                                    <div class="stepper-wrapper d-flex align-items-center">
                                                        <!--begin::Icon-->
                                                        <div class="stepper-icon w-40px h-40px">
                                                            <i class="stepper-check fas fa-check"></i>
                                                            <span class="stepper-number">2</span>
                                                        </div>
                                                        <!--begin::Icon-->

                                                        <!--begin::Label-->
                                                        <div class="stepper-label">
                                                            <h3 class="stepper-title">
                                                                Step 2
                                                            </h3>

                                                            <div class="stepper-desc">
                                                                Contract details
                                                            </div>
                                                        </div>
                                                        <!--end::Label-->
                                                    </div>
                                                    <!--end::Wrapper-->

                                                    <!--begin::Line-->
                                                    <div class="stepper-line h-40px"></div>
                                                    <!--end::Line-->
                                                </div>
                                                <!--end::Step 2-->

                                                <!--begin::Step 3-->
                                                <div class="stepper-item me-5<?php if(isset($contract_id)) echo ' completed'; ?>" data-kt-stepper-element="nav">
                                                    <!--begin::Wrapper-->
                                                    <div class="stepper-wrapper d-flex align-items-center">
                                                        <!--begin::Icon-->
                                                        <div class="stepper-icon w-40px h-40px">
                                                            <i class="stepper-check fas fa-check"></i>
                                                            <span class="stepper-number">3</span>
                                                        </div>
                                                        <!--begin::Icon-->

                                                        <!--begin::Label-->
                                                        <div class="stepper-label">
                                                            <h3 class="stepper-title">
                                                                Step 3
                                                            </h3>

                                                            <div class="stepper-desc">
                                                                Allowed trips
                                                            </div>
                                                        </div>
                                                        <!--end::Label-->
                                                    </div>
                                                    <!--end::Wrapper-->

                                                    <!--begin::Line-->
                                                    <div class="stepper-line h-40px"></div>
                                                    <!--end::Line-->
                                                </div>
                                                <!--end::Step 3-->

                                                <!--begin::Step 4-->
                                                <div class="stepper-item me-5<?php if(isset($contract_id)) echo ' current completed'; ?>" data-kt-stepper-element="nav">
                                                    <!--begin::Wrapper-->
                                                    <div class="stepper-wrapper d-flex align-items-center">
                                                        <!--begin::Icon-->
                                                        <div class="stepper-icon w-40px h-40px">
                                                            <i class="stepper-check fas fa-check"></i>
                                                            <span class="stepper-number">4</span>
                                                        </div>
                                                        <!--begin::Icon-->

                                                        <!--begin::Label-->
                                                        <div class="stepper-label">
                                                            <h3 class="stepper-title">
                                                                Step 4
                                                            </h3>

                                                            <div class="stepper-desc">
                                                                Success
                                                            </div>
                                                        </div>
                                                        <!--end::Label-->
                                                    </div>
                                                    <!--end::Wrapper-->

                                                    <!--begin::Line-->
                                                    <div class="stepper-line h-40px"></div>
                                                    <!--end::Line-->
                                                </div>
                                                <!--end::Step 4-->
                                            </div>
                                            <!--end::Nav-->
                                        </div>

                                        <!--begin::Content-->
                                        <div class="flex-row-fluid">
                                            <!--begin::Form-->
                                            <form id="add_contract_form" class="form w-lg-500px mx-auto" method="post" action="">
                                                <input hidden style="display:none;" name="add_edit_contract" value="add_edit_contract" type="text">
								                <?php wp_nonce_field( 'contract_nonce', 'contract_nonce' ); ?>
                                                <!--begin::Group-->
                                                <div class="mb-5">
                                                    <!--begin::Step 1-->
                                                    <div class="flex-column <?php if(!isset($contract_id)) echo ' current'; ?>" data-kt-stepper-element="content">
                                                        <!--begin::Input group-->
                                                        <div class="fv-row mb-10">
                                                            <!--begin::Label-->
                                                            <label class="form-label required">Title</label>
                                                            <!--end::Label-->

                                                            <!--begin::Input-->
                                                            <input type="text" required class="form-control form-control-solid" id="contract_title" name="contract_title" value="<?php echo $contract['title'] ?? '';?>"/>
                                                            <!--end::Input-->
                                                        </div>
                                                        <!--end::Input group-->

                                                        <!--begin::Input group-->
                                                        <div class="fv-row mb-10">
                                                            <!--begin::Label-->
                                                            <label class="form-label required">End Date</label>
                                                            <!--end::Label-->

                                                            <!--begin::Input-->
                                                            <input type="text" required class="form-control form-control-solid" id="contract_end_date" name="contract_end_date" value="<?php echo $contract['end_date'] ?? '';?>"/>
                                                            <!--end::Input-->
                                                        </div>
                                                        <!--end::Input group-->

                                                        <!--begin::Input group-->
                                                        <div class="fv-row mb-10">
                                                            <!--begin::Label-->
                                                            <label class="form-label" for="fright_companies">
                                                                <span class="">Company</span>
                                                                <span class="required" aria-required="true"></span>
                                                            </label>
                                                            <!--end::Label-->
                                                            <select required name="contact_company" class="form-select form-select-solid" data-control="select2" data-placeholder="Select an option" data-allow-clear="true"  id="fright_companies">
                                                                <option></option>
												                <?php
												                $companies = get_user_meta(get_current_user_id(),'fright_company');
												                foreach ($companies as $company_id){
													                $company_profile = get_user_by( 'ID', $company_id );
													                if(!$company_profile)
														                continue;
													                ?>
                                                                    <option <?php if( ( $contract['contracted_company_id'] ?? '' ) == $company_id ) echo 'selected = "selected"'; ?> value="<?php echo $company_profile->user_email;?>"><?php echo $company_profile->display_name.' ('.$company_profile->user_email.')';?></option>
													                <?php
												                }
												                ?>
                                                            </select>
                                                            <div class="pt-2">
												                <?php
												                $addCompanyPage = get_pages(array(
													                'meta_key' => '_wp_page_template',
													                'meta_value' => 'templates/page-add-company.php'
												                ));
												                if(isset($addCompanyPage[0])){
													                $addCompanyPageLink = get_permalink($addCompanyPage[0]->ID);
												                } else
													                $addCompanyPageLink = '#';
												                ?>
                                                                <a href="#" class="btn btn-sm btn-light btn-active-primary" data-bs-toggle="modal" data-bs-target="#kt_modal_new_company">
                                                                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
                                                                    <span class="svg-icon svg-icon-3">
                                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                                        <rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1" transform="rotate(-90 11.364 20.364)" fill="currentColor"></rect>
                                                        <rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="currentColor"></rect>
                                                    </svg>
                                                </span>
                                                                    <!--end::Svg Icon-->
                                                                    Add a new Company
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <!--end::Input group-->

                                                        <!--begin::Input group-->
                                                        <div class="row g-9" data-kt-buttons="true" data-kt-buttons-target="[data-kt-button]" data-kt-initialized="1">
                                                            <!--begin::Col-->
                                                            <div class="col-md-6">
                                                                <label class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6" data-kt-button="true">
                                                                    <!--begin::Radio button-->
                                                                    <span class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                                    <input class="form-check-input" type="radio" name="contract_type" value="containers" id="container_contract" <?php if( ( $contract['type'] ?? '' ) == 'containers' || ( $contract['type'] ?? '' ) == '') echo 'checked = "checked"'; ?>>
                                                                </span>
                                                                    <!--end::Radio button-->
                                                                    <span class="ms-5">
                                                                    <span class="fs-4 fw-bold mb-1 d-block">Number of containers</span>
                                                                    <span class="fw-semibold fs-7 text-gray-600">Contract tracks through number of containers shipped</span>
                                                                </span>
                                                                </label>
                                                            </div>
                                                            <!--end::Col-->
                                                            <!--begin::Col-->
                                                            <div class="col-md-6">
                                                                <label class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex text-start p-6" data-kt-button="true">
                                                                    <!--begin::Radio button-->
                                                                    <span class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                        <input class="form-check-input" type="radio" name="contract_type" value="money" id="money_contract" <?php if( ( $contract['type'] ?? '' ) == 'money' ) echo 'checked = "checked"'; ?>>
                                                    </span>
                                                                    <!--end::Radio button-->
                                                                    <span class="ms-5">
                                                        <span class="fs-4 fw-bold mb-1 d-block">Money</span>
                                                        <span class="fw-semibold fs-7 text-gray-600">Contract tracks through money in allowed trips for containers shipped</span>
                                                    </span>
                                                                </label>
                                                            </div>
                                                            <!--end::Col-->
                                                        </div>
                                                        <!--end::Input group-->
                                                    </div>
                                                    <!--begin::Step 1-->

                                                    <!--begin::Step 2-->
                                                    <div class="flex-column" data-kt-stepper-element="content">
                                                        <!--begin::Input group-->
                                                        <div id="containers_data" class="fv-row mb-10">
                                                            <!--begin::Label-->
                                                            <label class="form-label">Number Of Containers</label>
                                                            <!--end::Label-->

                                                            <!--begin::Input-->
                                                            <input type="number" min="<?php echo $contract['maximum_money'] ?? 0;?>" step="1" class="form-control form-control-solid" id="contract_maximum_containers" name="contract_maximum_containers" placeholder="" value="<?php echo $contract['maximum_containers'] ?? '';?>"/>
                                                            <!--end::Input-->
                                                        </div>
                                                        <!--end::Input group-->

                                                        <!--begin::Input group-->
                                                        <div id="money_data" class="fv-row mb-10 d-none">
                                                            <!--begin::Label-->
                                                            <label for="contract_maximum_money" class="form-label">Maximum Money</label>
                                                            <!--end::Label-->

                                                            <!--begin::Input-->
                                                            <div class="input-group">
                                                                <div class="input-group-prepend">
                                                                    <select name="currency" class="form-control input-group-text form-control-solid" style="border-top-right-radius: 0;  border-bottom-right-radius: 0;">
                                                                        <option value="USD" <?php if( ( $contract['currency'] ?? '' ) == 'USD' ) echo 'selected = "selected"'; ?>>USD</option>
                                                                        <option value="SAR" <?php if( ( $contract['currency'] ?? '' ) == 'SAR' ) echo 'selected = "selected"'; ?>>SAR</option>
                                                                    </select>
                                                                </div>
                                                                <input type="number" class="form-control form-control-solid" min="<?php echo $contract['maximum_money'] ?? 0;?>" placeholder="999.9" step="0.01" id="contract_maximum_money" name="contract_maximum_money" value="<?php echo $contract['maximum_money'] ?? '';?>"/>
                                                            </div>
                                                            <!--end::Input-->
                                                        </div>
                                                        <!--end::Input group-->
                                                    </div>
                                                    <!--begin::Step 2-->

                                                    <!--begin::Step 3-->
                                                    <div id="contract_step_3" class="flex-column" data-kt-stepper-element="content">
                                                        <!--begin::Input group-->
                                                        <div class="fv-row mb-10">
                                                            <!--begin::Label-->
                                                            <label class="form-label d-flex align-items-center">
                                                                <span class="required">Allowed Trips</span>
                                                                <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title="Allowed trips tip"></i>
                                                            </label>
                                                            <!--end::Label-->

                                                            <div class="table-responsive">
                                                                <input type="hidden" required="required" value="<?php if( isset( $contract['trips'] ) && is_array( $contract['trips'] ) && ! empty( $contract['trips'] ) ) echo 'valid';?>" id="trips_validator">
                                                                <table class="table table-striped gy-7 gs-7" id="trips_table">
                                                                    <thead>
                                                                    <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                                                                        <th>Origin</th>
                                                                        <th>Destination</th>
                                                                        <th class="trip_cost <?php if( ( $contract['type'] ?? '' ) == 'money' ) echo 'd-none';?>">Cost</th>
                                                                        <th class="text-end">Actions</th>
                                                                    </tr>
                                                                    </thead>
                                                                    <tbody id="trips_table_body">
													                <?php
													                if( isset( $contract['trips'] ) && is_array( $contract['trips'] ) ){
														                foreach ( $contract['trips'] as $trip ){
															                ?>
                                                                            <tr>
                                                                                <td><?php echo $trip['origin'];?></td>
                                                                                <td><?php echo $trip['destination'];?></td>
                                                                                <td class="trip_cost <?php if( ( $contract['type'] ?? '' ) == 'money' ) echo 'd-none';?>"><?php echo $trip['cost'];?></td>
                                                                                <td class="text-end">
                                                                                    <button class="btn btn-hover-scale p-0 remove-trip" type="button">
                                                                                        <i class="bi bi-x-circle fs-2x text-danger"></i>
                                                                                    </button>
                                                                                    <input name="contract_trips[]" type="hidden" value="<?php echo $trip['origin'];?>||<?php echo $trip['destination'];?>||<?php echo $trip['cost'];?>" hidden="">
                                                                                </td>
                                                                            </tr>
															                <?php
														                }
													                }
													                ?>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                        <!--end::Input group-->
                                                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#add_trip_modal">
                                                            Add Trip
                                                        </button>
                                                        <div id="contract_edit_cost" class="form-check form-check-custom form-check-warning form-check-solid mt-3">
                                                            <input class="form-check-input" type="checkbox" name="contract_edit_cost" id="contract_edit_cost_checkbox" value="yes" <?php if( ( $contract['edit_cost'] ?? '' ) == 'yes' ) echo 'checked = "checked"'; ?>>
                                                            <label class="form-check-label" for="contract_edit_cost_checkbox">
                                                                Allow provider to edit cost
                                                            </label>
                                                        </div>
                                                    </div>
                                                    <!--begin::Step 3-->

                                                    <div class="<?php if(isset($contract_id)) echo ' current'; ?>" data-kt-stepper-element="content">
                                                        <div class="w-100 text-center">
                                                            <!--begin::Heading-->
                                                            <h1 class="fw-bold text-dark mb-3"><?php if( isset( $_POST['contract_id'] ) ) echo 'Contract Edited!'; else echo 'Contract Added!';?></h1>
                                                            <!--end::Heading-->
                                                            <!--begin::Description-->
                                                            <div class="text-muted fw-semibold fs-3">Contract saved successfully, and you can use it now.</div>
                                                            <!--end::Description-->
                                                            <!--begin::Illustration-->
                                                            <div class="text-center px-4 py-15">
                                                                <img src="<?php echo get_template_directory_uri().'/'; ?>assets/media/illustrations/sketchy-1/9.png" alt="" class="mw-100 mh-300px">
                                                            </div>
                                                            <!--end::Illustration-->
                                                        </div>
                                                    </div>
                                                </div>
                                                <!--end::Group-->

                                                <!--begin::Actions-->
								                <?php if(!isset($contract_id)){
									                ?>
                                                    <div class="d-flex flex-stack">
                                                        <!--begin::Wrapper-->
                                                        <div class="me-2">
                                                            <button type="button" class="btn btn-light btn-active-light-primary" data-kt-stepper-action="previous">
                                                                Back
                                                            </button>
                                                        </div>
                                                        <!--end::Wrapper-->

                                                        <!--begin::Wrapper-->
                                                        <div>
                                                            <button class="btn btn-primary" type="submit" data-kt-stepper-action="submit">
                                            <span class="indicator-label">
                                                Submit
                                            </span>
                                                            </button>
                                                            <button type="button" class="btn btn-primary" data-kt-stepper-action="next">
                                                                Continue
                                                            </button>
                                                        </div>
                                                        <!--end::Wrapper-->
                                                    </div>
									                <?php
								                } ?>
                                                <!--end::Actions-->
                                            </form>
                                            <!--end::Form-->
                                        </div>
                                    </div>
                                    <!--end::Stepper-->
                                </div>
                                <!--end::Card body-->
                            </div>
                            <!--end::Card-->
                        </div>
                        <!--end::Tab pane-->
                        <!--begin::Tab pane-->
                        <div class="tab-pane fade" id="edit_history" role="tab-panel">
                            <div class="card">
                                <!--begin::Card body-->
                                <div class="card-body">
                                    <!--begin::Tab Content-->
                                    <div class="tab-content">
                                        <!--begin::Timeline-->
                                        <div class="timeline">
							                <?php
							                if ( isset( $_GET['contract_id'] ) ) {
								                $editHistory = get_post_meta( intval( $_GET['contract_id'] ), '_edit_history' );
								                foreach ( array_reverse( $editHistory ) as $key => $editData ){
									                ?>
                                                    <!--begin::Timeline item-->
                                                    <div class="timeline-item">
                                                        <!--begin::Timeline line-->
                                                        <div class="timeline-line w-40px"></div>
                                                        <!--end::Timeline line-->
                                                        <!--begin::Timeline icon-->
                                                        <div class="timeline-icon symbol symbol-circle symbol-40px">
                                                            <div class="symbol-label bg-light">
                                                                <!--begin::Svg Icon | path: icons/duotune/art/art005.svg-->
                                                                <span class="svg-icon svg-icon-2 svg-icon-gray-500">
                                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                    <path opacity="0.3" d="M21.4 8.35303L19.241 10.511L13.485 4.755L15.643 2.59595C16.0248 2.21423 16.5426 1.99988 17.0825 1.99988C17.6224 1.99988 18.1402 2.21423 18.522 2.59595L21.4 5.474C21.7817 5.85581 21.9962 6.37355 21.9962 6.91345C21.9962 7.45335 21.7817 7.97122 21.4 8.35303ZM3.68699 21.932L9.88699 19.865L4.13099 14.109L2.06399 20.309C1.98815 20.5354 1.97703 20.7787 2.03189 21.0111C2.08674 21.2436 2.2054 21.4561 2.37449 21.6248C2.54359 21.7934 2.75641 21.9115 2.989 21.9658C3.22158 22.0201 3.4647 22.0084 3.69099 21.932H3.68699Z" fill="currentColor"></path>
                                                                    <path d="M5.574 21.3L3.692 21.928C3.46591 22.0032 3.22334 22.0141 2.99144 21.9594C2.75954 21.9046 2.54744 21.7864 2.3789 21.6179C2.21036 21.4495 2.09202 21.2375 2.03711 21.0056C1.9822 20.7737 1.99289 20.5312 2.06799 20.3051L2.696 18.422L5.574 21.3ZM4.13499 14.105L9.891 19.861L19.245 10.507L13.489 4.75098L4.13499 14.105Z" fill="currentColor"></path>
                                                                </svg>
                                                            </span>
                                                                <!--end::Svg Icon-->
                                                            </div>
                                                        </div>
                                                        <!--end::Timeline icon-->
                                                        <!--begin::Timeline content-->
                                                        <div class="timeline-content mb-10 mt-n1">
                                                            <!--begin::Timeline heading-->
                                                            <div class="pe-3 mb-5">
                                                                <!--begin::Title-->
                                                                <div class="fs-5 fw-semibold mb-2">Contract edited.</div>
                                                                <!--end::Title-->
												                <?php
												                $editor = get_user_by( 'ID', $editData['user_id'] );
												                ?>
                                                                <div class="text-muted me-2 fs-7">at <?php echo $editData[ 'date' ].' by '.$editor->user_login; ?></div>
                                                            </div>
                                                            <!--end::Timeline heading-->
                                                            <div class="overflow-auto border border-dashed border-gray-300 rounded min-w-700px p-7 card">
                                                                <div class="collapsible cursor-pointer rotate" data-bs-toggle="collapse" data-bs-target="#kt_docs_card_collapsible<?php echo $key;?>">
                                                                    <h5 class="card-title">Data :</h5>
                                                                    <div class="card-toolbar rotate-180">
                                                                    <span class="svg-icon svg-icon-1">
                                                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                                                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                                                <polygon points="0 0 24 0 24 24 0 24"></polygon>
                                                                                <path d="M12.2928955,6.70710318 C11.9023712,6.31657888 11.9023712,5.68341391 12.2928955,5.29288961 C12.6834198,4.90236532 13.3165848,4.90236532 13.7071091,5.29288961 L19.7071091,11.2928896 C20.085688,11.6714686 20.0989336,12.281055 19.7371564,12.675721 L14.2371564,18.675721 C13.863964,19.08284 13.2313966,19.1103429 12.8242777,18.7371505 C12.4171587,18.3639581 12.3896557,17.7313908 12.7628481,17.3242718 L17.6158645,12.0300721 L12.2928955,6.70710318 Z" fill="#000000" fill-rule="nonzero"></path>
                                                                                <path d="M3.70710678,15.7071068 C3.31658249,16.0976311 2.68341751,16.0976311 2.29289322,15.7071068 C1.90236893,15.3165825 1.90236893,14.6834175 2.29289322,14.2928932 L8.29289322,8.29289322 C8.67147216,7.91431428 9.28105859,7.90106866 9.67572463,8.26284586 L15.6757246,13.7628459 C16.0828436,14.1360383 16.1103465,14.7686056 15.7371541,15.1757246 C15.3639617,15.5828436 14.7313944,15.6103465 14.3242754,15.2371541 L9.03007575,10.3841378 L3.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" transform="translate(9.000003, 11.999999) rotate(-270.000000) translate(-9.000003, -11.999999) "></path>
                                                                            </g>
                                                                        </svg>
                                                                    </span>
                                                                    </div>
                                                                </div>
                                                                <div id="kt_docs_card_collapsible<?php echo $key;?>" class="collapse align-items-center">
                                                                    <div class="card-body">
                                                                        <div class="table-responsive">
                                                                            <table class="table">
                                                                                <thead>
                                                                                <tr class="fw-bold fs-6 text-gray-800">
                                                                                    <th>Field</th>
                                                                                    <th>Old</th>
                                                                                    <th class="bg-r">New</th>
                                                                                </tr>
                                                                                </thead>
                                                                                <tbody>
																                <?php
																                foreach ( $editData as $metaKey => $metaValue ){
																	                if ( 'user_id' === $metaKey || 'date' === $metaKey ){
																		                continue;
																	                }
																	                ?>
                                                                                    <tr <?php if( $metaValue['old'] != $metaValue['new'] ) echo 'class="table-active"'?> >
                                                                                        <td><?php echo $metaKey; ?></td>
                                                                                        <td>
																			                <?php
																			                if ( 'trips' === $metaKey ){
																				                ?>
                                                                                                <table>
                                                                                                    <thead>
                                                                                                    <tr class="fw-bold fs-6 text-gray-800">
                                                                                                        <th class="p-3">Origin</th>
                                                                                                        <th class="p-3">Destination</th>
                                                                                                        <th class="p-3">Cost</th>
                                                                                                    </tr>
                                                                                                    </thead>
																					                <?php
																					                foreach ( $metaValue['old'] as $trip ){
																						                echo '<tr><td class="p-3">'.$trip['origin'].'</td><td class="p-3">'.$trip['destination'].'</td><td class="p-3">'.$trip['cost'].'</td></tr>';
																					                }
																					                ?>
                                                                                                </table>
																				                <?php
																			                } else {
																				                echo $metaValue['old'];
																			                }
																			                ?>
                                                                                        </td>
                                                                                        <td>
																			                <?php
																			                if ( 'trips' === $metaKey ){
																				                ?>
                                                                                                <table>
                                                                                                    <thead>
                                                                                                    <tr class="fw-bold fs-6 text-gray-800">
                                                                                                        <th class="p-3">Origin</th>
                                                                                                        <th class="p-3">Destination</th>
                                                                                                        <th class="p-3">Cost</th>
                                                                                                    </tr>
                                                                                                    </thead>
																					                <?php
																					                foreach ( $metaValue['new'] as $trip ){
																						                echo '<tr ><td class="p-3">'.$trip['origin'].'</td><td class="p-3">'.$trip['destination'].'</td><td class="p-3">'.$trip['cost'].'</td></tr>';
																					                }
																					                ?>
                                                                                                </table>
																				                <?php
																			                } else {
																				                echo $metaValue['new'];
																			                }
																			                ?>
                                                                                        </td>
                                                                                    </tr>
																	                <?php
																                }
																                ?>
                                                                                </tbody>
                                                                            </table>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <!--end::Timeline content-->
                                                    </div>
                                                    <!--end::Timeline item-->
									                <?php
								                }
							                }
							                ?>
                                        </div>
                                        <!--end::Timeline-->
                                    </div>
                                    <!--end::Tab Content-->
                                </div>
                                <!--end::Card body-->
                            </div>
                        </div>
                        <!--end::Tab pane-->
                    </div>
                    <!--end::Tab content-->
                    <?php
                }
                ?>
            </div>
		</div>
		<!--end::Container-->
	</div>
	<!--end::Container-->
<?php
get_footer();
