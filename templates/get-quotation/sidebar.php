<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $quotation ) || empty( $service_provider ) ) {
	return;
}
?>
<!--begin::Sidebar-->
<div class="flex-lg-auto min-w-lg-300px w-lg-300px">
	<!--begin::Card-->
	<div class="card" data-kt-sticky="true" data-kt-sticky-name="invoice" data-kt-sticky-offset="{default: false, lg: '200px'}" data-kt-sticky-width="{lg: '250px', lg: '300px'}" data-kt-sticky-left="auto" data-kt-sticky-top="150px" data-kt-sticky-animation="false" data-kt-sticky-zindex="95">
		<!--begin::Card body-->
		<div class="card-body p-10">
			<h5 class="modal-title">Additional Services</h5>
			<!--begin::Separator-->
			<div class="separator separator-dashed mb-8"></div>
			<!--end::Separator-->
			<!--begin::Input group-->
			<div class="mb-8">
				<!--begin::Option-->
				<label class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack mb-5">
					<span class="form-check-label ms-0 fw-bolder fs-6 text-gray-700">Insurance?</span>
					<input form="kt_invoice_form" class="form-check-input" type="checkbox" id="insurance" name="insurance" value="Insurance" <?php if ( 'included' == ( $quotation['insurance'] ?? '' ) ) {
						echo 'checked="checked"';
					} ?>/> </label>
				<!--end::Option-->
				<!--begin::Option-->
				<label class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack mb-5">
					<span class="form-check-label ms-0 fw-bolder fs-6 text-gray-700">Customs Clearance</span>
					<input form="kt_invoice_form" class="form-check-input" type="checkbox" id="customs_clearance" name="customs-clearance" value="Customs Clearance" <?php if ( 'included' == ( $quotation['customs-clearance'] ?? '' ) ) {
						echo 'checked="checked"';
					} ?>/> </label>
				<!--end::Option-->
			</div>
			<!--begin::Separator-->
			<div class="separator separator-dashed mb-8"></div>
			<!--end::Separator-->
			<!--begin::Input group-->
			<?php
			$providers = $service_provider->get_public_service_providers();
			if ( ! empty( $providers ) ) {
				?>
				<div class="mb-8">
					<!--begin::Option-->
					<label class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack mb-5">
						<span class="form-check-label ms-0 fw-bolder fs-6 text-gray-700">Send quotation request to WAYZ providers ?</span>
						<input form="kt_invoice_form" class="form-check-input" type="checkbox" id="send_wayz_providers" value="yes" name="send_wayz_providers" <?php if ( 'yes' == ( $quotation['send_wayz_providers'] ?? '' ) ) {
							echo 'checked="checked"';
						} ?>/> </label>
					<!--end::Option-->
				</div>
				<?php
			}
			?>
			<!--end::Input group-->
			<!--begin::Separator-->
			<div class="separator separator-dashed mb-8"></div>
			<!--end::Separator-->
			<div class="mb-8">
				<label class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack mb-5">
					<span class="form-check-label ms-0 fw-bolder fs-6 text-gray-700">Validity days</span>
					<span class="required" aria-required="true"></span>
					<input class="form-control form-control-solid" type="number" min="1" form="kt_invoice_form" name="validity_days" placeholder="1" value="<?php echo $quotation['validity_days'] ?? '30'; ?>"  required/>
				</label>
			</div>
			<!--begin::Separator-->
			<div class="separator separator-dashed mb-8"></div>
			<!--end::Separator-->
			<!--begin::Actions-->
			<div class="mb-0">
				<button form="kt_invoice_form" type="submit" class="btn btn-primary w-100" id="quotationForm_submit_button">
					<!--begin::Svg Icon | path: icons/duotune/general/gen016.svg-->
					<span class="svg-icon svg-icon-3">
						<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
							<path d="M15.43 8.56949L10.744 15.1395C10.6422 15.282 10.5804 15.4492 10.5651 15.6236C10.5498 15.7981 10.5815 15.9734 10.657 16.1315L13.194 21.4425C13.2737 21.6097 13.3991 21.751 13.5557 21.8499C13.7123 21.9488 13.8938 22.0014 14.079 22.0015H14.117C14.3087 21.9941 14.4941 21.9307 14.6502 21.8191C14.8062 21.7075 14.9261 21.5526 14.995 21.3735L21.933 3.33649C22.0011 3.15918 22.0164 2.96594 21.977 2.78013C21.9376 2.59432 21.8452 2.4239 21.711 2.28949L15.43 8.56949Z" fill="currentColor"/>
							<path opacity="0.3" d="M20.664 2.06648L2.62602 9.00148C2.44768 9.07085 2.29348 9.19082 2.1824 9.34663C2.07131 9.50244 2.00818 9.68731 2.00074 9.87853C1.99331 10.0697 2.04189 10.259 2.14054 10.4229C2.23919 10.5869 2.38359 10.7185 2.55601 10.8015L7.86601 13.3365C8.02383 13.4126 8.19925 13.4448 8.37382 13.4297C8.54839 13.4145 8.71565 13.3526 8.85801 13.2505L15.43 8.56548L21.711 2.28448C21.5762 2.15096 21.4055 2.05932 21.2198 2.02064C21.034 1.98196 20.8409 1.99788 20.664 2.06648Z" fill="currentColor"/>
						</svg>
					</span>
					<!--end::Svg Icon-->
					Get A Quotation
				</button>
			</div>
			<!--end::Actions-->
		</div>
		<!--end::Card body-->
	</div>
	<!--end::Card-->
</div><!--end::Sidebar-->
