<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $quotation ) ) {
	return;
}
?>
<!--begin::Table-->
<table id="airFrightFieldsTable" class="table g-5 gs-0 mb-0 fw-bolder text-gray-700" data-kt-element="items">
	<!--begin::Table head-->
	<thead>
	<tr class="border-bottom fs-7 fw-bolder text-gray-700 text-uppercase">
		<th class="min-w-300px w-475px">Gross weight</th>
		<th class="min-w-100px w-100px">#Pallets</th>
		<th class="min-w-100px w-100px">#Quantity</th>
	</tr>
	</thead>
	<!--end::Table head-->
	<!--begin::Table body-->
	<tbody> <?php
	$quantity         = empty( $quotation['quantity'] ) ? [ '1' ] : $quotation['quantity'];
	$air_pallets      = empty( $quotation['air_pallets'] ) ? [ '1' ] : $quotation['air_pallets'];
	$air_weight       = empty( $quotation['air_weight'] ) ? [ '' ] : $quotation['air_weight'];
	$air_weight_uom   = empty( $quotation['air_weight_uom'] ) ? [ '' ] : $quotation['air_weight_uom'];
	$air_dimensions_l = empty( $quotation['air_dimensions_l'] ) ? [ '' ] : $quotation['air_dimensions_l'];
	$air_dimensions_w = empty( $quotation['air_dimensions_w'] ) ? [ '' ] : $quotation['air_dimensions_w'];
	$air_dimensions_h = empty( $quotation['air_dimensions_h'] ) ? [ '' ] : $quotation['air_dimensions_h'];
	$air_latest_index = 0;
	foreach ( $air_weight as $key => $air_container_wight ) {
		if ( ! isset( $air_pallets[ $key ] ) ) {
			continue;
		}
		?>
		<tr class="border-bottom border-bottom-dashed" data-kt-element="item">
			<td class="pe-7">
				<div class="d-flex">
					<label>
						<input class="form-control form-control-solid" type="number" min="1" name="air_weight[item<?php echo $key; ?>]" placeholder="1" value="<?php echo $air_container_wight; ?>" data-kt-element="air_weight[<?php echo $key; ?>]" required/>
					</label>
					<select name="air_weight_uom[item<?php echo $key; ?>]" id="air_weight_uom" class="form-select" data-control="select2" data-placeholder="Select an option">
						<option value="KG" <?php if ( $air_weight_uom == 'KG' ) {echo 'selected="selected"';} ?>>KG</option>
						<option value="TON" <?php if ( $air_weight_uom == 'TON' ) {echo 'selected="selected"';} ?>>Ton</option>
					</select>
				</div>
				<div class="mt-3">
					<label> Dimensions in CM </label>
					<span class="d-flex justify-content-around">
						<label>
							L
							<input class="form-control form-control-solid" type="number" min="1" name="air_dimensions_l[item<?php echo $key; ?>]" placeholder="1" value="<?php echo $air_dimensions_l[ $key ]; ?>" data-kt-element="air_dimensions_l[<?php echo $key; ?>]" required/>
						</label>
						<label>
							W
							<input class="form-control form-control-solid" type="number" min="1" name="air_dimensions_w[item<?php echo $key; ?>]" placeholder="1" value="<?php echo $air_dimensions_w[ $key ]; ?>" data-kt-element="air_dimensions_w[<?php echo $key; ?>]" required/>
						</label>
						<label>
							H
							<input class="form-control form-control-solid" type="number" min="1" name="air_dimensions_h[item<?php echo $key; ?>]" placeholder="1" value="<?php echo $air_dimensions_h[ $key ]; ?>" data-kt-element="air_dimensions_h[<?php echo $key; ?>]" required/>
						</label>
					</span>
				</div>
			</td>
			<td class="ps-0">
				<span class="d-flex align-items-center">
					<label>
						<input class="form-control form-control-solid" type="number" min="1" name="air_pallets[item<?php echo $key; ?>]" placeholder="1" value="<?php echo $air_pallets[ $key ]; ?>" data-kt-element="air_pallets[<?php echo $key; ?>]" required/>
					</label>
				</span>
			</td>
			<td class="ps-0">
				<span class="d-flex align-items-center">
					<label>
						<input class="form-control form-control-solid" type="number" min="1" name="quantity[item<?php echo $key; ?>]" placeholder="1" value="<?php echo $quantity[ $key ]; ?>" data-kt-element="quantity[<?php echo $key; ?>]" required/>
					</label>
					<button class="btn btn-link py-1 m-1" data-kt-element="remove-item">
						<i class="icon-xl la la-remove btn-danger text-danger text-hover-dark" style="font-size: 20px!important;"></i>
					</button>
				</span>
			</td>
		</tr>
		<?php
		$air_latest_index ++;
	} ?>
	</tbody>
	<!--end::Table body-->
	<!--begin::Table foot-->
	<tfoot>
	<tr class="border-top border-top-dashed align-top fs-6 fw-bolder text-gray-700">
		<th class="text-primary">
			<button type="button" class="btn btn-link py-1" data-kt-element="add-item">Add Container</button>
		</th>
	</tr>
	</tfoot>
	<!--end::Table foot-->
</table>
<!--begin::Item template-->
<table id="air-template" class="table d-none" data-kt-element="item-template">
	<input id="airContainerIndex" type="number" disabled value="<?php echo $air_latest_index; ?>" style="display: none;" hidden>
	<tr class="border-bottom border-bottom-dashed" data-kt-element="item">
		<td class="pe-7">
			<div class="d-flex">
				<label>
					<input class="form-control form-control-solid" type="number" min="1" name="" placeholder="1" data-kt-element=""/>
				</label>
				<select name="" id="air_weight_uom" class="form-select change air_weight_uom" data-control="select2" data-placeholder="Select an option">
					<option value="KG">KG</option>
					<option value="TON">Ton</option>
				</select>
			</div>
			<div class="mt-3">
				<label> Dimensions in CM </label>
				<span class="d-flex justify-content-around">
					<label>
			  L
			  <input class="form-control form-control-solid change air_dimensions_l" type="number" min="1" name="" placeholder="1" data-kt-element=""/>
          </label>
					<label>
			  W
			  <input class="form-control form-control-solid change air_dimensions_w" type="number" min="1" name="" placeholder="1" data-kt-element=""/>
          </label>
					<label>
			  H
			  <input class="form-control form-control-solid change air_dimensions_h" type="number" min="1" name="" placeholder="1" data-kt-element=""/>
          </label>
				</span>
			</div>
		</td>
		<td class="ps-0">
			<label>
				<input required class="form-control form-control-solid palletsNameToChange" type="number" min="1" name="" placeholder="1" value="1" data-kt-element=""/>
			</label>
		</td>
		<td class="ps-0 d-flex align-items-center">
			<label>
				<input required class="form-control form-control-solid quantityNameToChange" type="number" min="1" name="" placeholder="1" value="1" data-kt-element=""/>
			</label>
			<button class="btn btn-link py-1 m-1" data-kt-element="remove-item">
				<i class="icon-xl la la-remove btn-danger text-danger text-hover-dark" style="font-size: 20px!important;"></i>
			</button>
		</td>
	</tr>
</table>
<!--end::Item template-->