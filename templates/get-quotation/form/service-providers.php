<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $quotation ) || ! isset( $companies ) ) {
	return;
}
?>
<div class="d-flex flex-column mb-8 fv-row">
	<!--begin::Label-->
	<label class="d-flex align-items-center fs-6 fw-bold mb-2" for="fright_companies[]">
		<span class="">Fright Forwarder Email</span>
		<span class="required" aria-required="true"></span>
		<i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title="" data-bs-original-title="Specify a target priorty" aria-label="Specify a target priorty"></i>
	</label>
	<select required name="fright_companies[]" class="form-select form-select-solid" data-control="select2" data-placeholder="Select an option" data-allow-clear="true" multiple="multiple" id="fright_companies">
		<option></option>
		<?php
		foreach ( $companies as $company_data ) {
			$company_profile = get_user_by( 'ID', $company_data['id'] );
			?>
			<option value="<?php echo $company_profile->user_email; ?>" <?php if ( in_array( $company_profile->user_email, ( $quotation['fright_companies'] ?? [] ) ) ) { echo 'selected="selected"'; } ?>>
				<?php echo $company_data['name'] . ' (' . $company_profile->user_email . ')'; ?>
			</option>
			<?php
		}
		?>
	</select>
	<div class="pt-2">
		<a href="#" class="btn btn-sm btn-light btn-active-primary" data-bs-toggle="modal" data-bs-target="#kt_modal_new_company">
			<!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
			<span class="svg-icon svg-icon-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
					<rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1" transform="rotate(-90 11.364 20.364)" fill="currentColor"></rect>
					<rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="currentColor"></rect>
				</svg>
			</span>
			<!--end::Svg Icon-->
			Add a new Company
		</a>
	</div>
</div>
