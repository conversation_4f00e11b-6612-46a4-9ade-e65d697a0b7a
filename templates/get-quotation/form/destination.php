<?php
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $quotation ) || ! isset( $addresses ) || ! isset( $countries ) ) {
	return;
}
?>
<div class="mb-4">
	<label for="destination_location" class="form-label">
		Destination Location
	</label>
	<span class="required" aria-required="true"></span>
	<select required name="destination_location" id="destination_location" class="form-select address-select" data-control="select2" data-placeholder="Select an option">
		<option value="">Select a location...</option>
		<option value="map_selector" <?php if ( 'map' == ( $quotation['destination_location_type'] ?? '' ) ) { echo 'selected="selected"'; } ?>>Select on map</option>
		<?php
		foreach ( $addresses as $address ) {
			$address_text = $address['address1'] . ',' . $address['city'] . ',' . $address['country'];
			?>
			<option value="<?php echo $address_text; ?>" <?php if ( $address_text == ( $quotation['destination_location'] ?? '' ) ) { echo 'selected="selected"'; } ?>><?php echo $address_text; ?></option>
			<?php
		}
		?>
	</select>
	<div class="pt-2">
		<a href="#" id="destination_location_button" class="btn btn-sm btn-light btn-active-primary" data-bs-toggle="modal" data-bs-target="#kt_modal_new_address">
			<!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
			<span class="svg-icon svg-icon-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
					<rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1" transform="rotate(-90 11.364 20.364)" fill="currentColor"></rect>
					<rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="currentColor"></rect>
				</svg>
			</span>
			<!--end::Svg Icon-->
			Add a new Address
		</a>
	</div>
	<div id="destination_google_map_selector" class="pt-2">
		<div id="destination_google_map" style="max-width:100%; width:800px;height:400px;"></div>
		<div class="row pt-2">
			<div class="col-6">
				<label for="destination_location_lat" class="form-label">
					Location lat
				</label>
				<span class="required" aria-required="true"></span>
				<input id="destination_location_lat" type="text" value="<?php echo $quotation['destination_location_lat'] ?? '0'; ?>" class="form-control form-control-solid" data-required="1" name="destination_location_lat" required/>
			</div>
			<div class="col-6">
				<label for="destination_location_lng" class="form-label">
					Location lng
				</label>
				<span class="required" aria-required="true"></span>
				<input id="destination_location_lng" type="text" value="<?php echo $quotation['destination_location_lng'] ?? '0'; ?>" class="form-control form-control-solid" data-required="1" name="destination_location_lng" required/>
			</div>
		</div>
	</div>
</div>
<div class="form-group row mb-4">
	<div class="col-lg-12 col-md-12 col-sm-12">
		<div class="row">
			<div class="col-6">
				<label for="destination_country" class="form-label">
					Destination Country
				</label>
				<span class="required" aria-required="true"></span>
				<select required name="destination_country" id="destination_country" class="form-select " data-control="select2" data-placeholder="Select an option">
					<option value="" selected>Select a Country...</option>
					<?php
					foreach ( $countries as $country ) {
						?>
						<option value="<?php echo $country['country']; ?>" <?php if ( $country['country'] == ( $quotation['destination_country'] ?? '' ) ) { echo 'selected="selected"'; } ?>><?php echo WC()->countries->countries[ $country['country'] ] ?? $country['country']; ?></option>
						<?php
					}
					?>
				</select>
			</div>
			<div class="col-6">
				<label for="destination_port" class="form-label">
					Destination Port
				</label>
				<span class="required" aria-required="true"></span>
				<div class="destination_port">
					<select required name="destination_port" id="destination_port" class="form-select" data-control="select2" data-placeholder="Select an option">
						<option>-</option>
					</select>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mb-4">
    <label for="deliver_by_date" class="form-label">
        Deliver by Date
    </label>
    <span class="required" aria-required="true"></span>
    <input required="required" value="<?php echo $quotation['deliver_by_date'] ?? ''; ?>" class="form-control form-control-solid" placeholder="Select date and time" name="deliver_by_date" id="deliver_by_date"/>
</div>