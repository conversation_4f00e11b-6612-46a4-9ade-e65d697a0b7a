<?php

use Wayz\Utils;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $quotation ) ) {
	return;
}
?>
<!--begin::Table-->
<table id="containersTable" class="table g-5 gs-0 mb-0 fw-bolder text-gray-700" data-kt-element="items">
	<!--begin::Table head-->
	<thead>
	<tr class="border-bottom fs-7 fw-bolder text-gray-700 text-uppercase">
		<th class="min-w-300px w-475px">Containers’ specifications</th>
		<th class="min-w-100px w-100px">#Pallets</th>
		<th class="min-w-100px w-100px">#Containers</th>
	</tr>
	</thead>
	<!--end::Table head-->
	<!--begin::Table body-->
	<tbody>
	<?php
	$container_specification        = empty( $quotation['container-specification'] ) ? [ '' ] : $quotation['container-specification'];
	$quantity                       = empty( $quotation['quantity'] ) ? [ '1' ] : $quotation['quantity'];
	$pallets                        = empty( $quotation['pallets'] ) ? [ '1' ] : $quotation['pallets'];
	$quotationContainersLatestIndex = 0;
	foreach ( $quantity as $key => $currentContainerQuantity ) {
		?>
		<tr class="border-bottom border-bottom-dashed" data-kt-element="item">
			<td class="pe-7">
				<div class="form-check form-check-custom form-check-solid justify-content-between flex-wrap">
					<?php
					foreach ( Utils::get_container_types() as $type => $name ) {
						?>
						<div class="min-w-150px mt-2 mb-2">
							<label class="form-check-label">
								<input class="form-check-input" type="radio" <?php if ( isset( $container_specification[ $key ] ) && $type == $container_specification[ $key ] ) {
									echo 'checked="checked"';
								} ?> value="<?php echo $type; ?>" name="container-specification[item<?php echo $key; ?>]" required/>
								<?php echo $name; ?>
							</label>
						</div>
						<?php
					}
					?>
				</div>
			</td>
			<td class="ps-0">
				<label>
					<input class="form-control form-control-solid" type="number" min="1" name="pallets[item<?php echo $key; ?>]" placeholder="1" value="<?php echo $pallets[ $key ]; ?>" data-kt-element="pallets[<?php echo $key; ?>]" required/>
				</label>
			</td>
			<td class="ps-0 d-flex align-items-center">
				<label>
					<input class="form-control form-control-solid" type="number" min="1" name="quantity[item<?php echo $key; ?>]" placeholder="1" value="<?php echo $currentContainerQuantity; ?>" data-kt-element="quantity[<?php echo $key; ?>]" required/>
				</label>
				<button class="btn btn-link py-1 m-1" data-kt-element="remove-item">
					<i class="icon-xl la la-remove btn-danger text-danger text-hover-dark" style="font-size: 20px!important;"></i>
				</button>
			</td>
		</tr>
		<?php
		$quotationContainersLatestIndex ++;
	}
	?>
	</tbody>
	<!--end::Table body-->
	<!--begin::Table foot-->
	<tfoot>
	<tr class="border-top border-top-dashed align-top fs-6 fw-bolder text-gray-700">
		<th class="text-primary">
			<button type="button" class="btn btn-link py-1" data-kt-element="add-item">Add Container</button>
		</th>
	</tr>
	</tfoot>
	<!--end::Table foot-->
</table>
<!--begin::Item template-->
<table id="containerTemplate" class="table d-none" data-kt-element="item-template">
	<input id="containerIndex" type="number" disabled value="<?php echo $quotationContainersLatestIndex; ?>" style="display: none;" hidden>
	<tr class="border-bottom border-bottom-dashed" data-kt-element="item">
		<td class="pe-7">
			<div class="form-check form-check-custom form-check-solid justify-content-between flex-wrap">
				<?php
				foreach ( Utils::get_container_types() as $type => $name ) {
					?>
					<div class="min-w-150px mt-2 mb-2">
						<label class="form-check-label">
							<input class="form-check-input nameToChange" type="radio" value="<?php echo $type; ?>" name=""/>
							<?php echo $name; ?>
						</label>
					</div>
					<?php
				}
				?>
			</div>
		</td>
		<td class="ps-0">
			<label>
				<input required class="form-control form-control-solid palletsNameToChange" type="number" min="1" name="" placeholder="1" value="1" data-kt-element=""/>
			</label>
		</td>
		<td class="ps-0 d-flex align-items-center">
			<label>
				<input required class="form-control form-control-solid quantityNameToChange" type="number" min="1" name="" placeholder="1" value="1" data-kt-element=""/>
			</label>
			<button class="btn btn-link py-1 m-1" data-kt-element="remove-item">
				<i class="icon-xl la la-remove btn-danger text-danger text-hover-dark" style="font-size: 20px!important;"></i>
			</button>
		</td>
	</tr>
</table>
<!--end::Item template-->