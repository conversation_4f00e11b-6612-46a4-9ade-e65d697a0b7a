<?php

use Wayz\Utils;

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

if ( ! isset( $quotation ) || ! isset( $addresses ) || ! isset( $countries ) ) {
	return;
}
?>
<!--begin::Wrapper-->
<div class="mb-4">
	<label for="pickup_address" class="form-label">
		Pickup address
	</label>
	<span class="required" aria-required="true"></span>
	<select required name="pickup_address" id="pickup_address" class="form-select address-select" data-control="select2" data-placeholder="Select an option">
		<option value="">Select an address...</option>
		<option value="map_selector" <?php if ( 'map' == ( $quotation['pickup_address_type'] ?? '' ) ) { echo 'selected="selected"'; } ?>>Select on map</option>

		<?php
		foreach ( $addresses as $address ) {
			$address_text = $address['address1'] . ',' . $address['city'] . ',' . $address['country'];
			?>
			<option value="<?php echo $address_text; ?>" <?php if ( $address_text == ( $quotation['pickup_address'] ?? '' ) ) { echo 'selected="selected"'; } ?>><?php echo $address_text; ?></option>
			<?php
		}
		?>
	</select>
	<div class="pt-2">
		<a href="#" id="pickup_address_button" class="btn btn-sm btn-light btn-active-primary" data-type="shipping_address" data-bs-toggle="modal" data-bs-target="#kt_modal_new_address">
			<!--begin::Svg Icon | path: icons/duotune/arrows/arr075.svg-->
			<span class="svg-icon svg-icon-3">
				<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
					<rect opacity="0.5" x="11.364" y="20.364" width="16" height="2" rx="1" transform="rotate(-90 11.364 20.364)" fill="currentColor"></rect>
					<rect x="4.36396" y="11.364" width="16" height="2" rx="1" fill="currentColor"></rect>
				</svg>
			</span>
			<!--end::Svg Icon-->
			Add a new Address
		</a>
	</div>
	<div id="pickup_google_map_selector" class="pt-2">
		<div id="pickup_google_map" style="max-width:100%; width:800px; height:400px;"></div>
		<div class="row pt-2">
			<div class="col-6">
				<label for="pickup_location_lat" class="form-label">
					Location lat
				</label>
				<span class="required" aria-required="true"></span>
				<input id="pickup_location_lat" type="text" value="<?php echo $quotation['pickup_address_lat'] ?? '0'; ?>" class="form-control form-control-solid" data-required="1" name="pickup_location_lat" required/>
			</div>
			<div class="col-6">
				<label for="pickup_location_lng" class="form-label">
					Location lng
				</label>
				<span class="required" aria-required="true"></span>
				<input id="pickup_location_lng" type="text" value="<?php echo $quotation['pickup_address_lng'] ?? '0'; ?>" class="form-control form-control-solid" data-required="1" name="pickup_location_lng" required/>
			</div>
		</div>
	</div>
</div>
<div class="form-group row mb-4">
	<div class="col-lg-12 col-md-12 col-sm-12">
		<div class="row">
			<div class="col-6">
				<label for="origin_country" class="form-label">
					Origin Country
				</label>
				<span class="required" aria-required="true"></span>
				<select required name="origin_country" id="origin_country" class="form-select " data-control="select2" data-placeholder="Select an option">
					<option value="" selected>Select a Country...</option>
					<?php
					foreach ( $countries as $country ) {
						?>
						<option value="<?php echo $country['country']; ?>" <?php if ( $country['country'] == ( $quotation['origin_country'] ?? '' ) ) {
							echo 'selected="selected"';
						} ?>><?php echo WC()->countries->countries[ $country['country'] ] ?? $country['country']; ?></option>
						<?php
					}
					?>
				</select>
			</div>
			<div class="col-6">
				<label for="origin_port" class="form-label">
					Origin Port
				</label>
				<span class="required" aria-required="true"></span>
				<div class="origin_port">
					<select required id="origin_port" name="origin_port" class="form-select" data-control="select2" data-placeholder="Select an option">
						<option>-</option>
					</select>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="mb-4">
	<label for="pick_up_date" class="form-label">
        Pick date & time
	</label>
	<span class="required" aria-required="true"></span>
	<input required="required" value="<?php echo $quotation['pick_up_date'] ?? ''; ?>" class="form-control form-control-solid" placeholder="Select date and time" name="pick_up_date" id="pick_up_date"/>
</div>