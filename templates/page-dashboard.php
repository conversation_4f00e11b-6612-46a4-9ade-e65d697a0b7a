<?php
/**
 * Template Name: User Dashboard
 */

get_header();

$user_id = get_current_user_id();
$is_admin = current_user_can( 'manage_options' );
?>
	<!--begin::Post-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<?php
			if ( $is_admin ) {
				?>
				<!--begin::Admin row-->
				<div class="row mb-xl-10">
					<div class="col-xl-12">
						<?php
						wc_get_template( 'admin-blocks/user-filter.php', [ ], '', get_template_directory() . '/templates/' );
						?>
					</div>
				</div>
				<!--ecd::Admin row-->
				<?php

				wc_get_template( 'admin-blocks/statistics.php', [ 'user_id' => $user_id, 'overview' => $is_admin ], '', get_template_directory() . '/templates/' );
			}
			?>
			<!--begin::Layout-->
			<div class="row gy-5 g-xl-10">
				<?php
				if ( ! $is_admin ) {
					?>
					<div class="col-xl-12 mb-xl-10">
						<?php
						wc_get_template( 'dashboard-blocks/savings.php', [ 'user_id' => $user_id ], '', get_template_directory() . '/templates/' );
						?>
					</div>
					<?php
				}
				?>
				<!--begin::Col-->
				<div class="col-xl-6 mb-xl-10">
					<?php
					wc_get_template( 'dashboard-blocks/active-shipments.php', [ 'user_id' => $user_id, 'overview' => $is_admin ], '',  get_template_directory() . '/templates/' );
					?>
				</div>
				<!--end::Col-->
				<!--begin::Col-->
				<div class="col-xl-6 mb-xl-10">
					<?php
					wc_get_template( 'dashboard-blocks/active-quotations.php', [ 'user_id' => $user_id, 'overview' => $is_admin ], '', get_template_directory() . '/templates/' );
					?>
				</div>
				<!--end::Col-->
			</div>
			<div class="row gy-5 g-xl-10">
				<!--begin::Col-->
				<div class="col-xl-12 mb-xl-10">
					<?php
					wc_get_template( 'dashboard-blocks/latest-offers.php', [ 'user_id' => $user_id, 'overview' => $is_admin ], '', get_template_directory() . '/templates/' );
					?>
				</div>
				<!--end::Col-->
			</div>
			<!--end::Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Post-->
<?php
get_footer();