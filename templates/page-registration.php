<?php
/**
 * Template Name: Registration
 */

use Wayz\Controllers\Organization;
use Wayz\Controllers\User;

if(isset($_POST['user_registration'])){
    /*
     * Create company user
     */
    $user_id = ( new User() )->create( $_POST['work_email'], [
            'first_name'    => $_POST['first_name'],
            'last_name'     => $_POST['last_name'],
            'job_title'     => $_POST['job_title'],
            'company_name'  => $_POST['company_name'],
            'phone_number'  => $_POST['phone_number'],
    ] );

    /*
     * Create Client Company
     */
	$company_id = ( new Organization() )->create( array(
		'company_name' => $_POST['company_name'],
		'phone_number' => $_POST['phone_number'],
		'monthly_shipments_avg' => $_POST['monthly_shipments_avg'],
	) );

    if ( $company_id ){
	    update_user_meta($user_id,'client_organization',$company_id);
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<meta name="viewport" content="width=device-width, initial-scale=1" />
	<meta property="og:locale" content="en_US" />
	<link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700" />
	<link href="<?php echo get_template_directory_uri().'/'; ?>assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
	<link href="<?php echo get_template_directory_uri().'/'; ?>assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
	<title></title>
</head>

<body id="kt_body" class="bg-body">
<div class="d-flex flex-column flex-root">
	<div class="d-flex flex-column flex-column-fluid bgi-position-y-bottom position-x-center bgi-no-repeat bgi-size-contain bgi-attachment-fixed">
		<div class="d-flex flex-center flex-column-auto p-10 justify-content-between bg-light pt-5 pb-5">
			<div class="d-flex fw-bold fs-6">
				<a href="<?php echo home_url();?>" class="text-muted text-hover-primary px-2">
					<img alt="logo" src="<?php echo get_template_directory_uri().'/'; ?>assets/images/logo_wayz.png" class="logo-white h-25px h-md-30px">
				</a>

			</div>
			<div class="d-flex align-items-center fw-bold fs-6">
				<a href="#" class="text-muted text-hover-primary px-2">Why Wayz</a>
				<a href="#" class="text-muted text-hover-primary px-2">Services</a>
				<a href="#" class="text-muted text-hover-primary px-2">Contact Us</a>
			</div>
			<div class="d-flex align-items-center fw-bold fs-6">
				<a href="<?php echo get_page_url_by_template('templates/page-login.php');?>" type="button" class="btn btn-secondary">Login</a>
			</div>
		</div>
		<div class="d-flex flex-center flex-column flex-column-fluid p-10 pb-lg-20">
            <?php
            if(!isset($user_id) || is_wp_error($user_id)){
                ?>
                <div class="w-lg-600px bg-body rounded shadow-sm p-10 p-lg-15 mx-auto">
                    <form class="form w-100" method="post" action="">
                        <div class="mb-10 text-center">
                            <h1 class="text-dark mb-3">Registration</h1>
                        </div>
                        <?php
                        if(isset($user_id) && is_wp_error($user_id)){
                            ?>
                            <div class="alert alert-danger d-flex align-items-center p-5 mb-10">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                                <span class="svg-icon svg-icon-2hx svg-icon-danger me-4">
														<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
															<path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
															<path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
														</svg>
                            </span>
                                <!--end::Svg Icon-->
                                <div class="d-flex flex-column">
                                    <h4 class="mb-1 text-danger">Email Exist</h4>
                                    <span>This email already exist!</span>
                                </div>
                            </div>
                        <?php
                        }
                        ?>
                        <div class="fv-row mb-7">
                            <label for="first_name" class="form-label fw-bolder text-dark fs-6">First Name</label>
                            <span class="required" aria-required="true"></span>
                            <input id="first_name" class="form-control form-control-lg form-control-solid" type="text" placeholder="" name="first_name" value="<?php if(isset($_POST['first_name'])) echo $_POST['first_name'];?>" required/>
                        </div>
                        <div class="fv-row mb-7">
                            <label for="last_name" class="form-label fw-bolder text-dark fs-6">Last Name</label>
                            <span class="required" aria-required="true"></span>
                            <input id="last_name" class="form-control form-control-lg form-control-solid" type="text" placeholder="" name="last_name" value="<?php if(isset($_POST['last_name'])) echo $_POST['last_name'];?>" required/>
                        </div>

                        <div class="fv-row mb-7">
                            <label for="job_title" class=" fs-5 fw-bold mb-2">Job Title</label>
                            <input type="text" class="form-control form-control-solid" placeholder="" id="job_title" name="job_title" />
                        </div>

                        <div class="fv-row mb-7">
                            <label for="company_name" class=" fs-5 fw-bold mb-2">Company Name</label>
                            <span class="required" aria-required="true"></span>
                            <input type="text" class="form-control form-control-solid" placeholder="" id="company_name" name="company_name" value="<?php if(isset($_POST['company_name'])) echo $_POST['company_name'];?>" required/>
                        </div>

                        <div class="fv-row mb-7">
                            <label for="phone_number" class=" fs-5 fw-bold mb-2">Phone Number</label>
                            <span class="required" aria-required="true"></span>
                            <input type="text" class="form-control form-control-solid" placeholder="" value="<?php if(isset($_POST['phone_number'])) echo $_POST['phone_number'];?>" id="phone_number" name="phone_number" required/>
                        </div>

                        <div class="d-flex flex-column mb-8 fv-row">
                            <label class="required fs-5 fw-bold mb-2">
                                Average number of monthly shipments
                            </label>
                            <div class="d-flex flex-stack gap-3 mb-3">
	                            <label class="btn btn-light-primary w-100" for="ch010">
		                            0 - 10
		                            <input id="ch010" type="radio" name="monthly_shipments_avg" class="d-none" data-kt-modal-bidding="option" value="0 - 10">
	                            </label>
	                            <label class="btn btn-light-primary w-100" for="ch11100">
		                            11 - 100
		                            <input id="ch11100" type="radio" name="monthly_shipments_avg" class="d-none" data-kt-modal-bidding="option" value="11 - 100">
	                            </label>
	                            <label class="btn btn-light-primary w-100" for="ch101400">
		                            101 - 400
		                            <input id="ch101400" type="radio" name="monthly_shipments_avg" class="d-none" data-kt-modal-bidding="option" value="101 - 400">
	                            </label>
	                            <label class="btn btn-light-primary w-100" for="ch400">
		                            400+
		                            <input id="ch400" type="radio" name="monthly_shipments_avg" class="d-none" data-kt-modal-bidding="option" value="+400">
	                            </label>
                            </div>
                        </div>

                        <div class="fv-row mb-7">
                            <label for="work_email" class="form-label fw-bolder text-dark fs-6">Work Email </label>
                            <span class="required" aria-required="true"></span>
                            <input class="form-control form-control-lg form-control-solid" value="<?php if(isset($_POST['work_email'])) echo $_POST['work_email'];?>" type="email" placeholder="" id="work_email" name="work_email" required/>
                        </div>

                        <div class="fv-row mb-10">
                            <label class="form-check form-check-custom form-check-solid form-check-inline">
                                <input required class="form-check-input" type="checkbox" name="toc" value="1" />
                                <span class="form-check-label fw-bold text-gray-700 fs-6">I Agree
									<a href="#" class="ms-1 link-primary">Terms and conditions</a>.</span>
                            </label>
                        </div>

                        <div class="text-center">
                            <input type="submit" class="btn btn-lg btn-primary w-100 mb-5" value="Submit" name="user_registration">
                        </div>
                    </form>
                </div>
	            <?php
            }else{
                ?>
                <div class="w-lg-600px bg-body rounded shadow-sm mx-auto ">
                    <div class="card card-custom bg-success justify-content-center align-items-center">
                        <div class="card-header justify-content-center">
                            <div class="card-title">
								<span class="card-icon">
									<i class="flaticon2-chat-1 text-white"></i>
								</span>
                                <h3 class="card-label text-white fw-bold fw-1">Almost there!</h3>
                            </div>

                        </div>
                        <div class="separator separator-solid separator-white opacity-20"></div>
                        <div class="card-body text-white">A team member will contact you within one Business Day</div>
                    </div>
                </div>
	            <?php
            }
            ?>
		</div>
	</div>
</div>
<script src="<?php echo get_template_directory_uri().'/'; ?>assets/plugins/global/plugins.bundle.js"></script>
<script src="<?php echo get_template_directory_uri().'/'; ?>assets/js/scripts.bundle.js"></script>
<script src="<?php echo get_template_directory_uri().'/'; ?>assets/js/custom/authentication/sign-up/general.js"></script>
</body>
</html>
