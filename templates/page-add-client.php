<?php
/**
 * Template Name: Add new Client
 */


if ( ! current_user_can( 'manage_options' ) ) {
	wp_redirect( get_bloginfo( 'url' ) );
}

get_header( '' );

use Wayz\Controllers\Organization;
use Wayz\Controllers\User;

if ( isset( $_POST['add_client'] ) ) {
	/*
	 * Create company user
	 */
	$user_id = ( new User() )->create( $_POST['work_email'], [
		'first_name'   => $_POST['first_name'],
		'last_name'    => $_POST['last_name'],
		'job_title'    => $_POST['job_title'],
		'phone_number' => $_POST['phone_number'],
		'client_organization' => $_POST['user_client_organization']
	] );
}

if ( ! isset( $_POST['add_client'] ) ) {
	?>
	<!--begin::Container-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<!--begin::Layout-->
			<div class="d-flex flex-column flex-lg-row">
				<!--begin::Content-->
				<div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
					<!--begin::Card-->
					<div class="card">
						<!--begin::Card body-->
						<div class="card-body p-12">
							<!--begin::Form-->
							<form action="<?php the_permalink();?>" method="post" id="add_client">
								<div class="row">
									<div class="col">
										<label class="form-label fs-3 fw-bolder text-gray-900 mb-6">Contact</label>
										<div class="mb-4">
											<label for="user_client_organization" class="form-label fs-6 fw-bolder text-gray-700">Company</label>
											<span class="required" aria-required="true"></span>
											<select class="form-select" name="user_client_organization" id="client_organization" required data-control="select2" data-placeholder="Select an option">
												<option selected>-</option>
												<?php
												$organizations = get_posts( array(
													'post_type'      => 'client_organization',
													'post_status'    => array( 'publish' ),
													'posts_per_page' => - 1
												) );
												foreach ( $organizations as $organization ) {
													?>
													<option value="<?php echo $organization->ID; ?>">
														<?php echo $organization->post_title; ?>
													</option>
													<?php
												}
												?>
											</select>
										</div>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700">First name</label>
											<span class="required" aria-required="true"></span>
											<input name="first_name" required type="text" class="form-control form-control-solid" placeholder="">
										</div>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700">Last name</label>
											<span class="required" aria-required="true"></span>
											<input name="last_name" required type="text" class="form-control form-control-solid" placeholder="">
										</div>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700">Job title</label>
											<span class="required" aria-required="true"></span>
											<input name="job_title" required type="text" class="form-control form-control-solid" placeholder="">
										</div>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700">Phone Number</label>
											<span class="required" aria-required="true"></span>
											<input name="phone_number" required type="number" class="form-control form-control-solid" placeholder="">
										</div>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700">Work Email</label>
											<span class="required" aria-required="true"></span>
											<input name="work_email" required type="email" class="form-control form-control-solid" placeholder="">
										</div>
									</div>
								</div>
								<div class="text-center">
                  <button type="reset" class="btn btn-light me-3">Cancel</button>
									<input type="submit" name="add_client" class="btn btn-primary" value="Save">
								</div>
							</form>
							<!--end::Form-->
						</div>
						<!--end::Card body-->
					</div>
					<!--end::Card-->
				</div>
				<!--end::Content-->
				<!--begin::Sidebar-->
				<div class="flex-lg-auto min-w-lg-300px">
					<!--begin::Card-->
					<div class="card" data-kt-sticky="true" data-kt-sticky-name="invoice"
					     data-kt-sticky-offset="{default: false, lg: '200px'}"
					     data-kt-sticky-width="{lg: '250px', lg: '300px'}" data-kt-sticky-left="auto"
					     data-kt-sticky-top="150px" data-kt-sticky-animation="false"
					     data-kt-sticky-zindex="95">
						<!--begin::Card body-->

						<!--end::Card body-->
					</div>
					<!--end::Card-->
				</div>
				<!--end::Sidebar-->
			</div>
			<!--end::Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Container-->
	<?php
} elseif ( isset( $user_id ) ) {
	$error = is_wp_error( $user_id );

	if ( $error ) {
		$msg = $user_id->get_error_message();
		$class = 'bg-danger';
	} else {
		$msg = 'Client Added';
		$class = 'bg-success';
	}
	?>
	<div class="d-flex flex-center flex-column flex-column-fluid">
		<div class="w-lg-600px bg-body rounded shadow-sm mx-auto ">
			<div class="card card-custom <?php echo $class;?> justify-content-center align-items-center">
				<div class="card-header justify-content-center">
					<div class="card-title">
						<h3 class="card-label text-white fw-bold fw-1"><?php echo $msg;?></h3>
					</div>
				</div>
			</div>
		</div>

	</div>
	<?php
}
?>
<?php
get_footer();