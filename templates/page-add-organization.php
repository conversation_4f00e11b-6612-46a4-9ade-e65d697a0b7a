<?php
/**
 * Template Name: Add new company / organization
 */


if ( ! current_user_can( 'manage_options' ) ) {
	wp_redirect( get_bloginfo( 'url' ) );
}

get_header( '' );

use Wayz\Controllers\Organization;

if ( isset( $_POST['add_organization'] ) ) {
	/*
     * Create Client Company
     */
	$company_id = ( new Organization() )->create( array(
		'company_name'  => $_POST['company_name'],
		'phone_number'  => $_POST['phone_number'],
	) );
}

if ( ! isset( $_POST['add_organization'] ) ) {
	?>
	<!--begin::Container-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<!--begin::Layout-->
			<div class="d-flex flex-column flex-lg-row">
				<!--begin::Content-->
				<div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
					<!--begin::Card-->
					<div class="card">
						<!--begin::Card body-->
						<div class="card-body p-12">
							<!--begin::Form-->
							<form action="<?php the_permalink();?>" method="post" id="add_organization">
								<div class="row">
									<div class="col">
										<label class="form-label fs-3 fw-bolder text-gray-900 mb-6"><?php echo the_title();?></label>
										<div class="mb-4">
											<label class="form-label fs-6 fw-bolder text-gray-700" for="company_name">Name</label>
											<span class="required" aria-required="true"></span>
											<input id="company_name" name="company_name" required type="text" class="form-control form-control-solid" placeholder="">
										</div>
										<div class="mb-4">
											<label for="phone_number" class="form-label fs-6 fw-bolder text-gray-700">Phone Number</label>
											<span class="required" aria-required="true"></span>
											<input id="phone_number" name="phone_number" required type="number" class="form-control form-control-solid" placeholder="">
										</div>
										<div class="mb-4">
											<label for="work_email" class="form-label fs-6 fw-bolder text-gray-700">Contact Email</label>
											<span class="required" aria-required="true"></span>
											<input id="work_email" name="work_email" required type="email" class="form-control form-control-solid" placeholder="">
										</div>
										<div class="mb-4">
											<label for="average_shipments" class="form-label fs-6 fw-bolder text-gray-700">Average number of monthly shipments</label>
											<input id="average_shipments" name="average_shipments" required type="number" class="form-control form-control-solid" placeholder="">
										</div>

										<div class="d-flex flex-column mb-4 fv-row">
											<label class="required fs-5 fw-bold mb-2">
												Average number of monthly shipments
											</label>
											<div class="d-flex flex-stack gap-3 mb-3">
												<label class="btn btn-light-primary w-100" for="ch010">
													0 - 10
													<input id="ch010" type="radio" name="monthly_shipments_avg" class="d-none" data-kt-modal-bidding="option" value="0 - 10">
												</label>
												<label class="btn btn-light-primary w-100" for="ch11100">
													11 - 100
													<input id="ch11100" type="radio" name="monthly_shipments_avg" class="d-none" data-kt-modal-bidding="option" value="11 - 100">
												</label>
												<label class="btn btn-light-primary w-100" for="ch101400">
													101 - 400
													<input id="ch101400" type="radio" name="monthly_shipments_avg" class="d-none" data-kt-modal-bidding="option" value="101 - 400">
												</label>
												<label class="btn btn-light-primary w-100" for="ch400">
													400+
													<input id="ch400" type="radio" name="monthly_shipments_avg" class="d-none" data-kt-modal-bidding="option" value="+400">
												</label>
											</div>
										</div>
									</div>
								</div>
								<div class="text-center">
									<button type="reset" class="btn btn-light me-3">Cancel</button>
									<input type="submit" name="add_organization" class="btn btn-primary" value="Save">
								</div>
							</form>
							<!--end::Form-->
						</div>
						<!--end::Card body-->
					</div>
					<!--end::Card-->
				</div>
				<!--end::Content-->
				<!--begin::Sidebar-->
				<div class="flex-lg-auto min-w-lg-300px">
					<!--begin::Card-->
					<div class="card" data-kt-sticky="true" data-kt-sticky-name="invoice"
					     data-kt-sticky-offset="{default: false, lg: '200px'}"
					     data-kt-sticky-width="{lg: '250px', lg: '300px'}" data-kt-sticky-left="auto"
					     data-kt-sticky-top="150px" data-kt-sticky-animation="false"
					     data-kt-sticky-zindex="95">
						<!--begin::Card body-->

						<!--end::Card body-->
					</div>
					<!--end::Card-->
				</div>
				<!--end::Sidebar-->
			</div>
			<!--end::Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Container-->
	<?php
} elseif ( isset( $company_id ) ) {
	$error = is_wp_error( $company_id );

	if ( $error ) {
		$msg = $company_id->get_error_message();
		$class = 'bg-danger';
	} else {
		$msg = 'Company Added';
		$class = 'bg-success';
	}
	?>
	<div class="d-flex flex-center flex-column flex-column-fluid">
		<div class="w-lg-600px bg-body rounded shadow-sm mx-auto ">
			<div class="card card-custom <?php echo $class;?> justify-content-center align-items-center">
				<div class="card-header justify-content-center">
					<div class="card-title">
						<h3 class="card-label text-white fw-bold fw-1"><?php echo $msg;?></h3>
					</div>
				</div>
			</div>
		</div>

	</div>
	<?php
}
?>
<?php
get_footer();