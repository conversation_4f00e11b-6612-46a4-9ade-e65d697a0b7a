<?php
/**
 * Template Name: Clients list
 */
if ( ! current_user_can( 'manage_options' ) ) {
	wp_redirect( get_bloginfo( 'url' ) );
}
get_header();
?>
	<div class="d-flex flex-column flex-column-fluid">
		<!--begin::Toolbar-->
		<div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
			<!--begin::Toolbar container-->
			<div id="kt_app_toolbar_container" class="app-container container-xxl d-flex flex-stack">
				<!--begin::Page title-->
				<div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
					<!--begin::Title-->
					<h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Customer Listing</h1>
					<!--end::Title-->
				</div>
				<!--end::Page title-->
				<!--begin::Actions-->
				<div class="d-flex align-items-center gap-2 gap-lg-3">
					<!--begin::Primary button-->
					<a href="<?php echo get_page_url_by_template('templates/page-add-client.php');?>" class="btn btn-sm fw-bold btn-primary" >Create</a>
					<!--end::Primary button-->
				</div>
				<!--end::Actions-->
			</div>
			<!--end::Toolbar container-->
		</div>
		<!--end::Toolbar-->
		<!--begin::Content-->
		<div id="kt_app_content" class="app-content flex-column-fluid">
			<!--begin::Content container-->
			<div id="kt_app_content_container" class="app-container container-xxl">
				<!--begin::Card-->
				<div class="card">
					<!--begin::Card body-->
					<div class="card-body pt-0">
						<!--begin::Table-->
						<div id="kt_customers_table_wrapper" class="dataTables_wrapper dt-bootstrap4 no-footer">
							<div class="table-responsive">
								<table class="table align-middle table-row-dashed fs-6 gy-5 dataTable no-footer" id="kt_customers_table">
									<!--begin::Table head-->
									<thead>
									<!--begin::Table row-->
										<tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
											<th class="min-w-125px sorting" tabindex="0" aria-controls="kt_customers_table" rowspan="1" colspan="1" style="width: 167.333px;" >Customer Name</th>
											<th class="min-w-125px sorting" tabindex="0" aria-controls="kt_customers_table" rowspan="1" colspan="1" style="width: 167.333px;" >Company</th>
											<th class="min-w-125px sorting" tabindex="0" aria-controls="kt_customers_table" rowspan="1" colspan="1" style="width: 207.267px;" >Email</th>
											<th class="min-w-125px sorting" tabindex="0" aria-controls="kt_customers_table" rowspan="1" colspan="1" style="width: 167.333px;" >Status</th>
											<th class="min-w-125px sorting" tabindex="0" aria-controls="kt_customers_table" rowspan="1" colspan="1" style="width: 218.933px;" >Created Date</th>
										</tr>
									<!--end::Table row-->
									</thead>
									<!--end::Table head-->
									<!--begin::Table body-->
									<tbody class="fw-semibold text-gray-600">
									<?php
									$clients = get_users(
										array(
											'role' => 'customer'
										)
									);

									foreach ( $clients as $client ) {
										?>
										<tr class="odd">
											<td>
												<?php echo $client->first_name . ' ' . $client->last_name ; ?>
											</td>

											<td>
												<?php echo get_user_meta( $client->ID,'company_name', true); ?>
											</td>
											<!--end::Name=-->
											<!--begin::Email=-->
											<td>
												<?php echo $client->user_email; ?>
											</td>
											<!--end::Email=-->
											<!--begin::Status=-->
											<td>
												<!--begin::Badges-->
												<div class="badge badge-light-success">Active</div>
												<!--end::Badges-->
											</td>
											<!--end::Status=-->
											<!--begin::Date=-->
											<td data-order="<?php echo $client->user_registered;?>"><?php echo $client->user_registered;?></td>
											<!--end::Date=-->
										</tr>
										<?php
									}
									?>
									</tbody>
									<!--end::Table body-->
								</table>
							</div>
						</div>
						<!--end::Table-->
					</div>
					<!--end::Card body-->
				</div>
				<!--end::Card-->
			</div>
			<!--end::Content container-->
		</div>
		<!--end::Content-->
	</div>
<?php
get_footer();