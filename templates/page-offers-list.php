<?php
/**
 * Template Name: Offers list
 */

use <PERSON>z\Controllers\Offers;
use <PERSON>z\Controllers\Quotations;
use <PERSON>z\Logger\QuotationHistory;
use Wayz\Permissions\Quotations_Offers;

get_header();
$quotation_id = $_GET['quotation_id'] ?? 0;
/*
 * if not exist redirect to 404
 */
if ( $quotation_id == 0 || false === get_post_status( $quotation_id ) ) {
	global $wp_query;
	$wp_query->set_404();
	status_header( 404 );
	get_template_part( 404 );
	exit();
}

$order_id = get_post_meta( $quotation_id, '_order_id', true );
if ( ! $order_id || $order_id == '' ) {
	if ( isset( $_GET['approve'] ) && $_GET['approve'] == 'approve' ) {
		$order_id = ( new \Wayz\Controllers\Orders() )->orderByOffer( $_GET['quotation_id'], $_GET['offer_id'] );
		if ( $order_id ) {
			$accepted_offer = get_post_meta( $quotation_id, '_accepted_offer', true );
		}
	}
} else {
	$accepted_offer = get_post_meta( $quotation_id, '_accepted_offer', true );
}

if ( isset( $_POST['rejected_offer_comment'] ) ) {
	( new Offers )->requestNewOffer( $quotation_id, $_POST['offer_id'], $_POST['rejected_offer_comment'] );
}

?>
	<div class="content d-flex flex-column flex-column-fluid pt-0">
		<div class="toolbar" id="kt_toolbar">
			<!--begin::Container-->
			<div id="kt_toolbar_container" class="container-fluid d-flex flex-stack">
				<!--begin::Page title-->
				<div data-kt-swapper="true" class="page-title d-flex align-items-center flex-wrap me-3 mb-5 mb-lg-0">
					<!--begin::Title-->
					<h1 class="d-flex text-dark fw-bolder fs-3 align-items-center my-1"><?php echo get_the_title( $quotation_id ) ?>
						- Offers
						<!--begin::Separator-->
						<span class="h-20px border-1 border-gray-200 border-start ms-3 mx-2 me-1"></span>
						<!--end::Separator-->
						<!--begin::Description-->
						<span class="text-muted fs-7 fw-bold mt-2"></span>
						<!--end::Description--></h1>
					<!--end::Title-->
				</div>
				<!--end::Page title-->
				<!--begin::Actions-->

				<!--end::Actions-->
			</div>
			<!--end::Container-->
		</div>
		<div class="post d-flex">
			<div id="kt_content_container" class="container-xxl">
				<div class="card mb-6">
					<div class="card-body pt-10 pb-10">
						<?php
						$is_quotation_receive_offers    = Quotations_Offers::is_quotation_receive_offers( $quotation_id );
						$quotation_receive_offers_until = Quotations_Offers::get_quotation_receive_offers_until( $quotation_id );

						if ( $is_quotation_receive_offers ) {
							?>
							<span class="position-absolute top-0 badge badge-success badge-lg mb-3">Accept offers ( <?php echo $quotation_receive_offers_until; ?> )</span>
							<?php
						} else {
							?>
							<span class="position-absolute top-0 badge badge-danger badge-lg mb-3">Offering closed</span>
							<?php
						}

						?>
						<div class="d-flex flex-wrap flex-sm-nowrap">
							<div class="me-7 mb-4">
								<div
									class="symbol symbol-100px symbol-lg-160px symbol-fixed position-relative">
								</div>
							</div>
							<div class="flex-grow-1">
								<div
									class="d-flex justify-content-between align-items-start flex-wrap mb-2">
									<div class="d-flex flex-column">

									</div>
								</div>
								<div class="d-flex flex-wrap flex-stack">
									<div class="d-flex flex-column flex-grow-1">
										<div class="d-flex flex-wrap">
											<div
												class="border border-gray-300 border-dashed rounded min-w-200px py-4 px-4 me-6 mb-3">
												<div class="fw-bold fs-6 text-gray-400"># of containers
												</div>
												<div class="d-flex align-items-center">
													<div class="fs-2 fw-bolder">
														<?php Quotations::generate_quotation_data( $quotation_id ); ?>
													</div>
												</div>
											</div>
											<?php
											$fields = array(
												'_shipment_type'        => __( 'Shipment type', 'wayz' ),
												'_load_type'            => __( 'Load type', 'wayz' ),
												'_incoterms'            => __( 'Incoterms', 'wayz' ),
												'_pickup_address'       => __( 'Pickup address ', 'wayz' ),
												'_pick_up_date'         => __( 'Pickup date', 'wayz' ),
												'_destination_location' => __( 'Destination Location', 'wayz' ),
												'_deliver_by_date'      => __( 'Deliver By Date', 'wayz' ),
												'_insurance'            => __( 'Insurance', 'wayz' ),
												'_customs-clearance'    => __( 'Customs Clearance', 'wayz' ),
											);
											foreach ( $fields as $key => $value ) {
												$result = get_post_meta( $quotation_id, $key, true );
												if ( $result == '' ) {
													continue;
												}
												?>
												<div class="border border-gray-300 border-dashed rounded min-w-200px py-4 px-4 me-6 mb-3">
													<div class="fw-bold fs-6 text-gray-400"><?php echo $value; ?></div>
													<div class="d-flex align-items-center">
														<div class="fs-2 fw-bolder"><?php
															if( strpos( $result, 'MAP::' ) !== false ) {
																$map = str_replace('MAP::','', $result);
																$result = 'https://www.google.com/maps/search/'.$map;
															}
															if ( strpos( $result, "http://" ) !== false || strpos( $result, "https://" ) !== false ) {
																echo '<a href="'. $result .'" target="_blank"><i class="bi bi-pin-map fs-2x text-primary"></i></a>';
															} else {
																echo $result;
															}
															?></div>
													</div>
												</div>
												<?php
											}
											?>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="container-xxl">
			<div class="card">
				<div class="card-header collapsible cursor-pointer rotate" data-bs-toggle="collapse" data-bs-target="#kt_docs_card_collapsible">
					<h3 class="card-title">Invited companies</h3>
					<div class="card-toolbar rotate-180">
                    <span class="svg-icon svg-icon-1">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1">
                          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                           <polygon points="0 0 24 0 24 24 0 24"></polygon>
                           <path d="M12.2928955,6.70710318 C11.9023712,6.31657888 11.9023712,5.68341391 12.2928955,5.29288961 C12.6834198,4.90236532 13.3165848,4.90236532 13.7071091,5.29288961 L19.7071091,11.2928896 C20.085688,11.6714686 20.0989336,12.281055 19.7371564,12.675721 L14.2371564,18.675721 C13.863964,19.08284 13.2313966,19.1103429 12.8242777,18.7371505 C12.4171587,18.3639581 12.3896557,17.7313908 12.7628481,17.3242718 L17.6158645,12.0300721 L12.2928955,6.70710318 Z" fill="#000000" fill-rule="nonzero"></path>
                           <path d="M3.70710678,15.7071068 C3.31658249,16.0976311 2.68341751,16.0976311 2.29289322,15.7071068 C1.90236893,15.3165825 1.90236893,14.6834175 2.29289322,14.2928932 L8.29289322,8.29289322 C8.67147216,7.91431428 9.28105859,7.90106866 9.67572463,8.26284586 L15.6757246,13.7628459 C16.0828436,14.1360383 16.1103465,14.7686056 15.7371541,15.1757246 C15.3639617,15.5828436 14.7313944,15.6103465 14.3242754,15.2371541 L9.03007575,10.3841378 L3.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" transform="translate(9.000003, 11.999999) rotate(-270.000000) translate(-9.000003, -11.999999) "></path>
                          </g>
                        </svg>
                    </span>
					</div>
				</div>
				<div id="kt_docs_card_collapsible" class="collapse">
					<div class="card-body">
						<!--begin::Table container-->
						<div class="table-responsive">
							<!--begin::Table-->
							<div id="kt_profile_overview_table_wrapper" class="dataTables_wrapper dt-bootstrap4 no-footer">
								<div class="table-responsive">
									<table id="kt_profile_overview_table"
									       class="table table-row-bordered table-row-dashed gy-4 align-middle fw-bolder dataTable no-footer">
										<!--begin::Head-->
										<thead class="fs-7 text-gray-400 text-uppercase">
										<tr>
											<th class="min-w-250px sorting" tabindex="0"
											    aria-controls="kt_profile_overview_table" rowspan="1" colspan="1"
											    style="width: 416.983px;"
											    aria-label="Manager: activate to sort column ascending">Company Name
											</th>
											<th class="min-w-90px sorting" tabindex="0"
											    aria-controls="kt_profile_overview_table" rowspan="1" colspan="1"
											    style="width: 170.95px;"
											    aria-label="Status: activate to sort column ascending">Status
											</th>
										</tr>
										</thead>
										<!--end::Head-->
										<!--begin::Body-->
										<tbody class="fs-6">
										<?php
										$service_providers = get_post_meta( $quotation_id, '_fright_companies', true );
										if ( 'yes' === get_post_meta( $quotation_id, '_send_to_wayz_providers', true ) ) {
											$providers = get_users(
												array(
													'role'       => 'shipping_company',
													'meta_query' => array(
														array(
															'key'     => '_provider_for_all',
															'value'   => 'yes',
															'compare' => '=='
														)
													)
												)
											);

											foreach ( $providers as $provider ) {
												$service_providers[] = $provider->user_email;
											}
										}
										foreach ( $service_providers as $email ) {
											$company_user = get_user_by( 'email', $email );
											if ( $company_user ) {
												$offering_status = Quotations_Offers::get_offering_status( $company_user->ID, $quotation_id );
												?>
												<tr class="odd">
													<td>
														<!--begin::User-->
														<div class="d-flex align-items-center">
															<!--begin::Wrapper-->
															<div class="me-5 position-relative">
																<!--begin::Avatar-->
																<div class="symbol symbol-35px symbol-circle">
																	<span class="symbol-label bg-light-danger text-danger fw-bold"><?php echo strtoupper( substr( $company_user->display_name, 0, 1 ) ); ?></span>
																</div>
																<!--end::Avatar-->
															</div>
															<!--end::Wrapper-->
															<!--begin::Info-->
															<div class="d-flex flex-column justify-content-center">
																<a href="" class="fs-6 text-gray-800 text-hover-primary"><?php echo $company_user->display_name; ?></a>
																<div class="fw-bold text-gray-400"><?php if ( 'yes' === get_user_meta( $company_user->ID, '_provider_for_all', true ) ) {
																		echo 'Ways Provider';
																	} else {
																		echo $company_user->user_email;
																	} ?></div>
															</div>
															<!--end::Info-->
														</div>
														<!--end::User-->
													</td>
													<td>
														<?php
														if ( 'pending' === $offering_status ) {
															$css_class = 'danger';
														} elseif ( 'offered' === $offering_status ) {
															$css_class = 'success';
														} else {
															$css_class = 'warning';
														}
														?>
														<span class="badge badge-light-<?php echo $css_class;?> fw-bolder px-4 py-3 text-capitalize">
                                                            <?php echo $offering_status; ?>
                                                        </span>
													</td>
												</tr>
												<?php
											}
										}
										?>
										</tbody>
										<!--end::Body-->
									</table>
								</div>
							</div>
							<!--end::Table-->
						</div>
						<!--end::Table container-->
					</div>
				</div>
			</div>
			<div class="card card-flush mt-3">
				<div class="card-body">
					<div class="table-responsive">
						<table class="table align-middle m-0">
							<tbody>
							<?php
							$comments = get_comments( array(
								'post_id' => $quotation_id,
								'parent'  => 0,
								'number'  => 9999
							) );
							foreach ( $comments as $comment ) {
								$isContract    = false;
								$contract_id   = get_post_meta( $quotation_id, '_company_' . get_comment_meta( $comment->comment_ID, 'author_id', true ) . '_contract_id', true );
								$validity_days = get_comment_meta( $comment->comment_ID, 'validity_days', true );
								$expired       = false;
								if ( $validity_days && '' != $validity_days ) {
									$valid_until = date( 'Y-m-d', strtotime( $comment->comment_date . ' + ' . $validity_days . ' days' ) );
									if ( $valid_until < date( 'Y-m-d' ) ) {
										$expired = true;
									}
								}

								if ( $contract_id && $contract_id != '' ) {
									$isContract = true;
								}

								$directTrip = get_comment_meta( $comment->comment_ID, 'direct_trip', true );
								?>
								<tr>
									<td class="text-center d-flex justify-content-around">
										<?php
										$displayData = [
											'transit_days'   => [ 'title' => 'TRANSIT TIME', 'type' => 'Days' ],
											'port_free_days' => [ 'title' => 'PORT FREE DAYS', 'type' => 'Days' ],
										];
										foreach ( $displayData as $field_key => $field ) {
											$fieldData = get_comment_meta( $comment->comment_ID, $field_key, true );
											if ( $fieldData && $fieldData != '' ) {
												?>
												<div class="d-flex align-items-center">
													<div class="d-flex justify-content-start flex-column align-items-center m-auto">
														<span class="fw-bolder text-hover-primary fs-2"><?php echo $fieldData; ?></span>
														<span class="fw-bold d-block fs-5"><?php echo $field['type']; ?></span>
														<span class="text-gray-400 fs-7"><?php echo $field['title']; ?></span>
													</div>
												</div>
												<?php
											}
										}
										?>
									</td>
									<td class="text-end pe-0">
										<span class="fw-bolder fs-4"><?php echo $comment->comment_author; ?></span>
									</td>
									<td class="text-end pe-0">
										<span class="fw-bolder fs-4"><?php echo get_comment_date( '', $comment->comment_ID ); ?></span>
									</td>
									<td class="text-end pe-12">
										<span class="fw-bolder fs-4"><?php echo get_comment_meta( $comment->comment_ID, 'offer_price', true ) . ' ' . get_comment_meta( $comment->comment_ID, 'offer_currency', true ); ?></span>
									</td>
									<td class="text-end">
										<div class="badges">
											<?php
											if ( 'yes' === $directTrip ) {
												?>
												<span class="badge badge-success badge-sm mb-3">Direct Trip</span>
												<?php
											} elseif ( 'no' === $directTrip ) {
												?>
												<span class="badge badge-secondary badge-sm mb-3">Indirect Trip</span>
												<?php
											}

											if ( ! $expired && '' != $validity_days ) {
												?>
												<span class="badge badge-primary badge-sm mb-3">Valid until <?php echo date( 'Y-m-d', strtotime( $comment->comment_date . ' + ' . $validity_days . ' days' ) ); ?></span>
												<?php
											} elseif ( $expired ) {
												?>
												<span class="badge badge-danger badge-sm mb-3">Expired</span>
												<?php
											}
											if ( $isContract ) {
												?>
												<span class="badge badge-primary badge-sm mb-3">Contract</span>
												<?php
											}
											?>
										</div>
										<?php
										if ( $order_id != '' && isset( $accepted_offer ) && $accepted_offer == $comment->comment_ID ) {
											?>
											<a href="<?php echo get_page_url_by_template( 'templates/page-order-details.php' ) . '?order_id=' . $order_id; ?>" class="btn btn-sm btn-primary btn-active-light-primary m-1">View Order</a>
											<?php
										} elseif ( $order_id == '' && ! $expired ) {
											$approve_link = "https://$_SERVER[HTTP_HOST]$_SERVER[REQUEST_URI]&approve=approve&offer_id=" . $comment->comment_ID;
											if ( 'pending' == Offers::get_status( $comment->comment_ID ) ) {
												?>
												<a href="#" class="btn btn-sm btn-success btn-active-light-success"
												   data-bs-toggle="modal" data-bs-target="#get_deffirent_price_<?php echo $comment->comment_ID; ?>">Get Different Price</a>
												<?php
											}
											?>
											<a href="<?php echo $approve_link; ?>" class="btn btn-sm btn-primary btn-active-light-primary m-1">Approve</a>
											<?php
										}
										?>
										<a href="#" class="btn btn-sm btn-secondary text-hover-black text-black text-center fw-normal m-1" data-bs-toggle="modal" data-bs-target="#offer_<?php echo $comment->comment_ID; ?>">See details</a>
									</td>
								</tr>
								<div class="modal fade" id="offer_<?php echo $comment->comment_ID; ?>" tabindex="-1"
								     style="display: none;" aria-hidden="true">
									<div class="modal-dialog modal-xl">
										<div class="modal-content rounded">
											<div class="modal-header justify-content-end border-0 pb-0">
												<div class="btn btn-sm btn-icon btn-active-color-primary"
												     data-bs-dismiss="modal">
                                                            <span class="svg-icon svg-icon-1">
										<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
										     viewBox="0 0 24 24" fill="none">
											<rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
											      transform="rotate(-45 6 17.3137)" fill="currentColor"></rect>
											<rect x="7.41422" y="6" width="16" height="2" rx="1"
											      transform="rotate(45 7.41422 6)" fill="currentColor"></rect>
										</svg>
									</span>
												</div>
											</div>
											<div class="modal-body pt-0 pb-10 px-5 px-xl-10">
												<div class="mb-8 text-center">
													<h1 class="mb-1">Offer Details</h1>
												</div>
												<div>
													<p>
														<?php echo $comment->comment_content; ?>
													</p>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="modal fade" id="get_deffirent_price_<?php echo $comment->comment_ID; ?>" tabindex="-1" style="display: none;"
								     aria-hidden="true">
									<div class="modal-dialog modal-xl">
										<div class="modal-content rounded">
											<div class="modal-header justify-content-end border-0 pb-0">
												<div class="btn btn-sm btn-icon btn-active-color-primary"
												     data-bs-dismiss="modal">
                                                            <span class="svg-icon svg-icon-1">
										<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
										     viewBox="0 0 24 24" fill="none">
											<rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1"
											      transform="rotate(-45 6 17.3137)" fill="currentColor"></rect>
											<rect x="7.41422" y="6" width="16" height="2" rx="1"
											      transform="rotate(45 7.41422 6)" fill="currentColor"></rect>
										</svg>
									</span>
												</div>
											</div>
											<div class="modal-body pt-0 pb-10 px-5 px-xl-10">
												<div>
													<form action="?quotation_id=<?php echo $quotation_id; ?>"
													      method="post" id="reject_form" class="comment-form">
														<div class="form-group">
															<label for="comment"
															       class="fw-bolder fs-4 mb-4">Comment</label>
															<textarea id="rejected_offer_comment" class="form-control"
															          name="rejected_offer_comment" aria-required="true"
															          required=""></textarea>
														</div>
														<div class="text-center">
															<button name="submit" id="submit" type="submit"
															        class="btn btn-primary">
																Send
															</button>
														</div>
														<input type="hidden" name="quotation_id"
														       value="<?php echo $quotation_id; ?>"
														       id="comment_post_ID">
														<input type="hidden" name="offer_id" id="offer_id"
														       value="<?php echo $comment->comment_ID; ?>">
													</form>
												</div>
											</div>
										</div>
									</div>
								</div>
								<?php
							}
							if ( empty( $comments ) ) {
								?>
								<div class="mb-2">
									<!--begin::Title-->
									<h2 class="fw-bold text-gray-400 text-center lh-lg">
										There is no New Offers
									</h2>
									<!--end::Title-->
									<!--begin::Illustration-->
									<div class="flex-grow-1 bgi-no-repeat bgi-size-contain bgi-position-x-center card-rounded-bottom h-200px mh-200px my-5 my-lg-12"
									     style="background-image:url('<?php echo get_template_directory_uri() . '/'; ?>assets/media/svg/illustrations/easy/2.svg')"></div>
									<!--end::Illustration-->
								</div>
								<?php
							}
							?>
							</tbody>
						</table>
					</div>
				</div>
			</div>
			<div class="card card-flush mt-3">
				<!--begin::Card header-->
				<div class="card-header">
					<div class="card-title">
						<h2>History</h2>
					</div>
				</div>
				<!--end::Card header-->
				<!--begin::Card body-->
				<div class="card-body">
					<?php QuotationHistory::print_quotation_history( $quotation_id ); ?>
				</div>
				<!--end::Card body-->
			</div>
		</div>
	</div>
<?php
get_footer();