<?php
/**
 * Template Name: User contracts
 */

use Wayz\Controllers\Contracts;

defined( 'ABSPATH' ) || exit;
get_header( '' );

if ( isset( $_GET['deactivate'] ) && 'deactivate' == $_GET['deactivate'] && isset( $_GET['contract_id'] ) ){
    ( new Contracts() )->diactivate( $_GET['contract_id'] );
}
?>
    <div class="post d-flex flex-column-fluid">
	    <div class="container-xxl">
		    <?php
		    if ( current_user_can( 'manage_options' ) ) {
			    ?>
			    <!--begin::Admin row-->
			    <div class="row mb-xl-10">
				    <div class="col-xl-12">
					    <?php
					    wc_get_template( 'admin-blocks/user-filter.php', [ ], '', get_template_directory() . '/templates/' );
					    ?>
				    </div>
			    </div>
			    <!--ecd::Admin row-->
			    <?php
		    }
		    global $current_user;
		    wc_get_template( 'dashboard-blocks/user-contracts.php', [ 'user_id' => $current_user->ID ], '', get_template_directory() . '/templates/' );
		    ?>
	    </div>
    </div>
<?php
get_footer();
