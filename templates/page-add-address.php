]<?php
/**
 * Template Name: Add new Address
 */

use Wayz\Controllers\Addresses;

defined( 'ABSPATH' ) || exit;
get_header( '' );

$user_id = get_current_user_id();

if ( isset( $_GET['address_id'] ) ) {
	$address_data = ( new Addresses() )->get( $_GET['address_id'] );
}

$meta = false;
if ( isset( $_POST['add_new_address'] ) || isset( $_POST['edit_address'] ) ) {
	$address = array(
		'country'       => $_POST['country'],
		'address1'      => $_POST['address1'],
		'address2'      => $_POST['address2'],
		'phone_number'  => $_POST['phone_number'],
		'email_address' => $_POST['email_address'],
		'city'          => $_POST['city'],
		'state'         => $_POST['state'],
		'postcode'      => $_POST['postcode']
	);

	if ( isset( $_GET['address_id'] ) ) {
		$address['ID'] = $_GET['address_id'];
		$meta          = ( new Addresses() )->edit( $address );
	} else {
		$meta = ( new Addresses() )->add( $address, $user_id );
	}
}
?>
<!--begin::Post-->
<?php
if ( ! isset( $_POST['add_new_address'] ) || ! $meta ) {
	?>
	<!--begin::Container-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<!--begin::Layout-->
			<div class="d-flex flex-column flex-lg-row">
				<!--begin::Content-->
				<div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
					<!--begin::Card-->
					<div class="card">
						<!--begin::Card body-->
						<div class="card-body p-12">
							<!--begin::Form-->
							<form class="form" method="post" action="">
								<input hidden style="display:none;" name="add_new_address" value="add_new_address" type="text">
								<!--begin::Modal header-->
								<h2>Add New Address</h2>
								<!--end::Modal header-->
								<!--begin::Modal body-->
								<div id="add_new_address_container">
									<!--begin::Input group-->
									<div class="d-flex flex-column mb-5 fv-row">
										<!--begin::Label-->
										<label class="d-flex align-items-center fs-5 fw-bold mb-2">
											<span class="required">Country</span>
											<i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title="Your payment statements may very based on selected country"></i>
										</label>
										<!--end::Label-->
										<!--begin::Select-->
										<select required name="country" id="address_company" data-control="select2" data-dropdown-parent="#add_new_address_container" data-placeholder="Select a Country..." class="form-select form-select-solid">
											<option value="">Select a Country...</option>
											<?php
											$countries = wayz_get_countries();
											foreach ( $countries as $country ) {
												$address_country = $address_data['country'] ?? '';
												$selected = ( $address_country == $country['country'] ? 'selected' : '' );
												echo '<option value="' . $country['country'] . '" ' . $selected .'>' . $country['country'] . '</option>';
											}
											?>
										</select>
										<!--end::Select-->
									</div>
									<!--end::Input group-->
									<!--begin::Input group-->
									<div class="d-flex flex-column mb-5 fv-row">
										<!--begin::Label-->
										<label class="required fs-5 fw-bold mb-2">City</label>
										<!--end::Label-->
										<!--begin::Input-->
										<input required class="form-control form-control-solid" placeholder="" name="city" value="<?php echo $address_data['city'] ?? '';?>"/>
										<!--end::Input-->
									</div>
									<!--end::Input group-->
									<!--begin::Input group-->
									<div class="row g-9 mb-5">
										<!--begin::Col-->
										<div class="col-md-6 fv-row">
											<!--begin::Label-->
											<label class="fs-5 fw-bold mb-2">State / Province</label>
											<!--end::Label-->
											<!--begin::Input-->
											<input class="form-control form-control-solid" placeholder="" name="state" value="<?php echo $address_data['state'] ?? '';?>"/>
											<!--end::Input-->
										</div>
										<!--end::Col-->
										<!--begin::Col-->
										<div class="col-md-6 fv-row">
											<!--begin::Label-->
											<label class="fs-5 fw-bold mb-2">Post Code</label>
											<!--end::Label-->
											<!--begin::Input-->
											<input class="form-control form-control-solid" placeholder="" name="postcode" value="<?php echo $address_data['postcode'] ?? '';?>"/>
											<!--end::Input-->
										</div>
										<!--end::Col-->
									</div>
									<!--end::Input group-->
									<!--begin::Input group-->
									<div class="d-flex flex-column mb-5 fv-row">
										<!--begin::Label-->
										<label class="required fs-5 fw-bold mb-2">Address Line 1</label>
										<!--end::Label-->
										<!--begin::Input-->
										<input class="form-control form-control-solid" placeholder="" name="address1" required value="<?php echo $address_data['address1'] ?? '';?>"/>
										<!--end::Input-->
									</div>
									<!--end::Input group-->
									<!--begin::Input group-->
									<div class="d-flex flex-column mb-5 fv-row">
										<!--begin::Label-->
										<label class="fs-5 fw-bold mb-2">Address Line 2</label>
										<!--end::Label-->
										<!--begin::Input-->
										<input class="form-control form-control-solid" placeholder="" name="address2" value="<?php echo $address_data['address2'] ?? '';?>"/>
										<!--end::Input-->
									</div>
									<!--end::Input group-->
									<div class="d-flex flex-column mb-5 fv-row">
										<!--begin::Label-->
										<label for="phone" class="fs-5 fw-bold mb-2">Phone Number</label>
										<!--end::Label-->
										<!--begin::Input-->
										<input class="form-control form-control-solid" placeholder="" name="phone_number" id="phone" type="number" inputmode="number" value="<?php echo $address_data['phone_number'] ?? '';?>"/>
										<!--end::Input-->
									</div>
									<div class="d-flex flex-column mb-5 fv-row">
										<!--begin::Label-->
										<label for="email_address" class="required fs-5 fw-bold mb-2">Email Address</label>
										<!--end::Label-->
										<!--begin::Input-->
										<input class="form-control form-control-solid" placeholder="" name="email_address" id="email_address" type="email" inputmode="email" required value="<?php echo $address_data['email_address'] ?? '';?>"/>
										<!--end::Input-->
									</div>
								</div>
								<!--end::Modal body-->
								<!--begin::Modal footer-->
								<div class="modal-footer flex-center">
									<!--begin::Button-->
									<button type="reset" id="kt_modal_new_address_cancel" class="btn btn-light me-3">Discard</button>
									<!--end::Button-->
									<!--begin::Button-->

									<button type="submit" class="btn btn-primary">
										<span class="indicator-label">Add</span>
									</button>
									<!--end::Button-->
								</div>
								<!--end::Modal footer-->
							</form>
							<!--end::Form-->
						</div>
						<!--end::Card body-->
					</div>
					<!--end::Card-->
				</div>
				<!--end::Content-->
			</div>
			<!--end::Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Container-->
	<?php
}
?>
<!--end::Post-->
<?php
if ( ( isset( $_POST['add_new_address'] ) || isset( $_POST['edit_address'] ) ) && $meta ) {
	?>
	<div class="d-flex flex-center flex-column flex-column-fluid">
		<div class="w-lg-600px bg-body rounded shadow-sm mx-auto ">
			<div class="card card-custom bg-success">
				<div class="card-header justify-content-center">
					<div class="card-title">
						<h3 class="card-label text-white fw-bold fw-1">Address added</h3>
					</div>
					<div class="card-body text-white text-center">
						<a href="<?php echo get_page_url_by_template( 'templates/page-addresses-list.php' ); ?>" class="btn btn-sm fw-bold btn-primary">Addresses List</a>
					</div>
				</div>
			</div>
		</div>
	</div>
	<?php
}

get_footer();