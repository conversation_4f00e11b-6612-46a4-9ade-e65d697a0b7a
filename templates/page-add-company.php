<?php
/**
 * Template Name: Add new company
 */

use Wayz\Controllers\ServiceProvider;

defined( 'ABSPATH' ) || exit;
get_header( '' );

$user_id          = get_current_user_id();
$service_provider = new ServiceProvider();

$company_id       = false;
$company_userdata = false;

if ( isset( $_GET['edit_company'] ) ) {
	$company_id       = (int) $_GET['edit_company'];
	$company_userdata = get_userdata( $company_id );
}

if ( isset( $_POST['edit_company'] ) || isset( $_POST['add_new_company'] ) ) {
	$company = [
		'ID'           => $_POST['edit_company'] ?? '',
		'company_name' => sanitize_text_field( $_POST['company_name'] ),
		'location'     => sanitize_text_field( $_POST['location'] ),
		'country'      => sanitize_text_field( $_POST['company_country'] ),
		'address1'     => sanitize_text_field( $_POST['address1'] ),
		'address2'     => sanitize_text_field( $_POST['address2'] ),
		'city'         => sanitize_text_field( $_POST['city'] ),
		'port'         => sanitize_text_field( $_POST['company_port'] ),
		'contact'      => sanitize_text_field( $_POST['contact'] ),
		'phone'        => sanitize_text_field( $_POST['phone'] ),
		'email'        => sanitize_email( $_POST['email'] )
	];

	if ( isset( $_POST['add_new_company'] ) ) {
		$meta = $service_provider->add_company( $company, $user_id );
	}

	if ( isset( $_POST['edit_company'] ) ) {
		$meta = $service_provider->edit_company( $company, $user_id );
	}
}

if ( ! isset( $_POST['add_new_company'] ) && ! isset( $_POST['edit_company'] ) ) {
	?>
	<!--begin::Container-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<!--begin::Layout-->
			<div class="d-flex flex-column flex-lg-row">
				<!--begin::Content-->
				<div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
					<!--begin::Card-->
					<div class="card">
						<!--begin::Card body-->
						<div class="card-body p-12">
							<!--begin::Form-->
							<form action="" method="post" id="new_company_form">
								<?php
								if ( ! isset( $_GET['edit_company'] ) ) {
									?>
									<input hidden style="display:none;" name="add_new_company" value="add_new_company" type="text">
									<?php
								} else {
									?>
									<input hidden style="display:none;" name="edit_company" value="<?php echo $_GET['edit_company']; ?>" type="text">
									<?php
								}
								?>
								<div class="row">
									<div class="col-xl-6">
										<label class="form-label fs-3 fw-bolder text-gray-900 mb-6">Company Form</label>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required" for="company_name">Company</label>
											<input type="text" value="<?php echo $company_userdata ? $company_userdata->display_name : ''; ?>" class="form-control form-control-solid" placeholder="" id="company_name" name="company_name" required/>
										</div>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required" for="company_location">Location</label>
											<input value="<?php echo $company_id ? get_user_meta( $company_id,
												'location',
												true ) : ''; ?>" id="company_location" name="location" type="text" class="form-control form-control-solid" placeholder="" required>
										</div>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required" for="company_country">Country</label>
											<select required name="company_country" id="company_country" class="form-select form-select-solid" data-control="select2" data-dropdown-parent="#new_company_form" data-placeholder="Select an option">
												<option>Select a Country...</option>
												<?php
												$countries = wayz_get_countries();
												foreach ( $countries as $country ) {
													$selected = $country['country'] == get_user_meta( $company_id,
														'country', true ) ? 'selected="selected"' : '';
													echo '<option ' . $selected . ' value="' . $country['country'] . '">' . $country['country'] . '</option>';
												}
												?>
											</select>
										</div>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required">City</label>
											<input name="city" value="<?php echo $company_id ? get_user_meta( $company_id,
												'city',
												true ) : ''; ?>" type="text" class="form-control form-control-solid" placeholder="" required>
										</div>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required">Port</label>
											<select required id="company_port" name="company_port" class="form-select" data-control="select2" data-dropdown-parent="#new_company_form" data-placeholder="Select an option">
												<option>-</option>
											</select>
										</div>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required">Address 1</label>
											<input value="<?php echo $company_id ? get_user_meta( $company_id,
												'address1',
												true ) : ''; ?>" name="address1" id="address1" type="text" class="form-control form-control-solid" placeholder="" required>
										</div>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700">Address 2</label>
											<input value="<?php echo $company_id ? get_user_meta( $company_id,
												'address2',
												true ) : ''; ?>" name="address2" type="text" class="form-control form-control-solid" placeholder="">
										</div>
									</div>

									<div class="col-xl-6">
										<label class="form-label fs-3 fw-bolder text-gray-900 mb-6">Details Option</label>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required">Name</label>
											<input value="<?php echo $company_id ? get_user_meta( $company_id,
												'contact',
												true ) : ''; ?>" name="contact" type="text" class="form-control form-control-solid" placeholder="" required>
										</div>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required">Phone Number</label>
											<input value="<?php echo $company_id ? get_user_meta( $company_id, 'phone',
												true ) : ''; ?>" name="phone" type="number" class="form-control form-control-solid" placeholder="" required>
										</div>
										<div class="mb-4 fv-row">
											<label class="form-label fs-6 fw-bolder text-gray-700 required">Email</label>
											<input value="<?php echo $company_userdata ? $company_userdata->user_email : ''; ?>" name="email" type="email" class="form-control form-control-solid" placeholder="" required>
										</div>
									</div>
								</div>
								<div class="text-center">
									<a href="<?php echo get_page_url_by_template( 'templates/page-service-providers-list.php' ); ?>" class="btn btn-light me-3">Cancel</a>
									<button type="submit" class="btn btn-primary">Save</button>
								</div>
							</form>
							<!--end::Form-->
						</div>
						<!--end::Card body-->
					</div>
					<!--end::Card-->
				</div>
				<!--end::Content-->
				<!--begin::Sidebar-->
				<div class="flex-lg-auto min-w-lg-300px w-lg-300px">
					<?php
					if ( isset( $_GET['edit_company'] ) ) {
						?>
						<!--begin::Card-->
						<div class="card">
							<!--begin::Card body-->
							<div class="card-body p-10">
								<h5 class="modal-title">Contacts</h5>
								<div class="separator separator-dashed mb-8"></div>
								<?php
								$service_provider_contacts = $service_provider->get_SP_contacts( $company_id );
								foreach ( $service_provider_contacts as $contact ) {
									?>
									<div class="mb-8">
										<div class="text-center bg-light card-rounded d-flex flex-column justify-content-center p-5">
											<h2 class="text-dark fw-bold my-5"><?php echo $contact->display_name; ?></h2>
											<div class="text-gray-700 fs-4"><?php echo update_user_meta( $contact->ID,
													'_phone', true ); ?></div>
											<div class="text-gray-700 fs-4"><?php echo $contact->user_email; ?></div>
										</div>
									</div>
									<?php
								}
								?>
							</div>
							<!--end::Card body-->
						</div>
						<!--end::Card-->
						<?php
					}
					?>
				</div>
				<!--end::Sidebar-->
			</div>
			<!--end::Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Container-->
	<?php
}

if ( ( isset( $_POST['add_new_company'] ) || isset( $_POST['edit_company'] ) ) && $meta ) {
	?>
	<div class="d-flex flex-center flex-column flex-column-fluid">
		<div class="w-lg-600px bg-body rounded shadow-sm mx-auto ">
			<div class="card card-custom bg-success justify-content-center align-items-center">
				<div class="card-header justify-content-center">
					<div class="card-title">
                                    <span class="card-icon">
                                        <i class="flaticon2-chat-1 text-white"></i>
                                    </span>
						<h3 class="card-label text-white fw-bold fw-1">Company added</h3>
					</div>

				</div>
				<div class="separator separator-solid separator-white opacity-20"></div>
				<div class="card-body text-white text-center">
					Company details added to your profile!
					<br><br>
					<a href="<?php echo get_page_url_by_template( 'templates/page-service-providers-list.php' ); ?>" class="btn btn-sm fw-bold btn-primary">Service Providers List</a>
				</div>
			</div>
		</div>

	</div>
	<?php
}
get_footer();