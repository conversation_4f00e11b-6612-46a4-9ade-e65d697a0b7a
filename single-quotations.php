<?php

use <PERSON>z\Controllers\Contracts;
use <PERSON>z\Controllers\Offers;
use <PERSON>z\Controllers\Quotations;
use Wayz\Permissions\Quotations_Offers;
use Wayz\Permissions\Roles\Role;
use Wayz\Permissions\Roles\SP_Contact;
use Wayz\Utils;

defined( 'ABSPATH' ) || exit;
get_header();
while ( have_posts() ) : the_post();
	$post_id                = get_the_ID();
	$quotation_offer_author = get_post_field( 'post_author', $post_id );
	$current_user_id        = get_current_user_ID();
	$incoterms              = get_post_meta( get_the_ID(), '_incoterms', true );

	if ( Role::is_service_provider_contact( $current_user_id ) ) {
		$current_user_id = ( new SP_Contact( $current_user_id ) )->user_id;
	}

	/*
	 * Decline offer
	 */
	if ( isset( $_GET['decline'] ) && 'decline' === $_GET['decline'] ) {
		Offers::decline_to_offer( $current_user_id, $post_id );
	}

	$offering_status = Quotations_Offers::get_offering_status( $current_user_id, $post_id, '' );

	if ( isset( $_POST['shipment_data'] ) ) {
		( new Quotations() )->update_shipment_data( $_POST, $post_id );
	}
	?>
	<!--begin::Post-->
	<div class="post d-flex flex-column-fluid" id="kt_post">
		<!--begin::Container-->
		<div id="kt_content_container" class="container-xxl">
			<!--begin::Layout-->
			<div class="d-flex flex-column flex-lg-row">
				<!--begin::Content-->
				<div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
					<!--begin::Card-->
					<div class="card">
						<!--begin::Card body-->
						<div class="card-body p-12">
							<!--begin::Form-->
							<div class="mb-0 bg-gray-100 p-5 rounded-4">
								<p class="fs-5 fw-bold">Client Name:
									<span class="fw-normal fs-6"><?php echo get_the_author_meta( 'display_name', $quotation_offer_author ); ?></span>
								</p>
								<p class="fs-5">
								<span class="fs-5 fw-bold">
									Address :
								</span>
									<span class="fs-5">
									<?php echo Utils::getFormattedAddress( get_post_meta( $post_id, '_pickup_address', true ) ); ?>
									<i class="icon-xl la la-arrow-right m-3" style="font-size: 20px !important;"></i>
									<?php echo Utils::getFormattedAddress( get_post_meta( $post_id, '_destination_location', true ) ); ?>
								</span>
								</p>
							</div>
							<div class="separator separator-dashed my-10"></div>
							<div class="mb-0">
								<table class="table table-hover mt-10 table-striped">
									<thead>
									<tr>
										<th class="fw-bolder fs-4">Shipment details</th>
									</tr>
									</thead>
									<tbody>
									<?php
									$shipment_type = get_post_meta( $post_id, '_shipment_type', true );
									$load_type     = get_post_meta( $post_id, '_load_type', true );
									?>
									<tr>
										<td class="fw-bold">Shipment type</td>
										<td><?php echo $shipment_type; ?></td>
									</tr>
									<tr>
										<td class="fw-bold">Load type</td>
										<td><?php echo $load_type; ?></td>
									</tr>
									<tr>
										<td class="fw-bold">Containers</td>
										<td>
											<?php Quotations::generate_quotation_data( $post_id ); ?>
										</td>
									</tr>
									<tr>
										<td class="fw-bold">Incoterms</td>
										<td><?php echo $incoterms; ?></td>
									</tr>
									</tbody>
								</table>
								<table class="table table-hover mt-10 table-striped">
									<thead>
									<tr>
										<th class="fw-bolder fs-4">Additional Services</th>
									</tr>
									</thead>
									<tbody>
									<tr>
										<td class="fw-bold">Insurance</td>
										<td><?php echo get_post_meta( $post_id, '_insurance', true ); ?></td>
									</tr>
									<tr>
										<td class="fw-bold">Customs Clearance</td>
										<td><?php echo get_post_meta( $post_id, '_customs-clearance', true ); ?></td>
									</tr>
									</tbody>
								</table>
								<table class="table table-hover mt-10 table-striped">
									<thead>
									<tr>
										<th class="fw-bolder fs-4">PickUp</th>
									</tr>
									</thead>
									<tbody>
									<?php
									$fields = array(
										'_pick_up_date'   => __( 'Pickup date', 'wayz' ),
										'_origin_country' => __( 'Country ', 'wayz' ),
										'_origin_port'    => __( 'Port', 'wayz' ),
									);
									foreach ( $fields as $key => $value ) {
										$result = get_post_meta( get_the_ID(), $key, true );
										if ( $result == '' ) {
											continue;
										}
										?>
										<tr>
											<td class="fw-bold"><?php echo $value; ?></td>
											<td><?php echo $result; ?></td>
										</tr>
										<?php
									}
									?>
									</tbody>
								</table>
								<table class="table table-hover mt-10 table-striped">
									<thead>
									<tr>
										<th class="fw-bolder fs-4">Destination</th>
									</tr>
									</thead>
									<tbody>
									<?php
									$fields = array(
										'_destination_country' => __( 'Country', 'wayz' ),
										'_destination_port'    => __( 'Port', 'wayz' )
									);
									foreach ( $fields as $key => $value ) {
										$result = get_post_meta( get_the_ID(), $key, true );
										if ( $result == '' ) {
											continue;
										}
										?>
										<tr>
											<td class="fw-bold"><?php echo $value; ?></td>
											<td><?php echo $result; ?></td>
										</tr>
										<?php
									}
									?>
									</tbody>
								</table>
								<div class="separator separator-dashed my-10"></div>
								<div class="mb-0">
									<h3 class="fw-bolder fs-4">Notes :</h3>
									<p class="fs-5">
										<?php
										echo get_post_meta( $post_id, '_note', true );
										?>
									</p>
								</div>
								<div class="separator separator-dashed my-10"></div>
							</div>
							<?php
							$args     = array(
								'user_id' => $current_user_id, // use user_id
								'post_id' => get_the_ID(),
								'parent'  => 0
							);
							$comments = get_comments( $args );
							$order_id = get_post_meta( get_the_ID(), '_order_id', true );
							if ( ! empty( $comments ) ){
								foreach ( $comments as $comment ){
									if ( $order_id && $order_id != '' ) {
										$accepted_offer = get_post_meta( get_the_ID(), '_accepted_offer', true );
									}
									?>
									<div class="card mb-5 mb-xl-8 <?php if ( isset( $accepted_offer ) && $accepted_offer == $comment->comment_ID ) { echo ' border border-active border-primary active'; } ?>">
										<!--begin::Body-->
										<div class="card-body pb-0">
											<!--begin::Header-->
											<div class="d-flex align-items-center mb-3">
												<!--begin::User-->
												<div class="d-flex align-items-center flex-grow-1">
													<!--begin::Avatar-->
													<div class="symbol symbol-45px me-5">
														<i class="bi bi-building fs-3x success"></i>
													</div>
													<!--end::Avatar-->
													<!--begin::Info-->
													<div class="d-flex flex-column">
														<a href="#" class="text-gray-900 text-hover-primary fs-6 fw-bolder"><?php echo $comment->comment_author; ?></a>
														<span class="text-gray-400 fw-bold"><?php echo $comment->comment_date; ?></span>
													</div>
													<!--end::Info-->
												</div>
												<!--end::User-->
											</div>
											<!--end::Header-->
											<!--begin::Post-->
											<div class="mb-7">
												<!--begin::Text-->
												<div class="text-gray-800 mb-5">
													<div class="text-gray-800 mb-5">
											<span>
												<span class="text-gray-400 fs-7">Validity Days : </span>
												<span class="fw-bolder fs-4"><?php echo get_comment_meta( $comment->comment_ID, 'validity_days', true ); ?> Days</span>
											</span>
														<span class="m-3">-</span>
														<span>
												<span class="text-gray-400 fs-7">Port free days : </span>
												<span class="fw-bolder fs-4"><?php echo get_comment_meta( $comment->comment_ID, 'port_free_days', true ); ?> Days</span>
											</span>
														<span class="m-3">-</span>
														<span>
												<span class="text-gray-400 fs-7">TRANSIT TIME : </span>
												<span class="fw-bolder fs-4"><?php echo get_comment_meta( $comment->comment_ID, 'transit_days', true ); ?> Days</span>
											</span>
														<span class="m-3">-</span>
														<span>
												<span class="text-gray-400 fs-7">OFFER : </span>
												<span class="fw-bolder fs-4"><?php echo get_comment_meta( $comment->comment_ID, 'offer_price', true ) . ' ' . get_comment_meta( $comment->comment_ID, 'offer_currency', true ); ?></span>
											</span>
													</div>
													<!--end::Text-->
													<!--begin::Text-->
													<div class="text-gray-800 mb-5"><?php echo $comment->comment_content; ?></div>
													<!--end::Text-->
												</div>
												<!--end::Post-->
												<!--begin::Replies-->
												<?php
												$args    = array(
													'parent'  => $comment->comment_ID,
													'post_id' => get_the_ID()
												);
												$replies = get_comments( $args );
												foreach ( $replies as $reply ) {
													?>
													<div class="mb-7 ps-10">
														<!--begin::Reply-->
														<div class="d-flex mb-5">
															<!--begin::Avatar-->
															<div class="symbol symbol-45px me-5">
																<i class="bi bi-person fs-3x success"></i>
															</div>
															<!--end::Avatar-->
															<!--begin::Info-->
															<div class="d-flex flex-column flex-row-fluid">
																<!--begin::Info-->
																<div class="d-flex align-items-center flex-wrap mb-1">
																	<a href="#" class="text-gray-800 text-hover-primary fw-bolder me-2"><?php echo $reply->comment_author; ?></a>
																	<span class="text-gray-400 fw-bold fs-7"><?php echo $reply->comment_date; ?></span>
																</div>
																<!--end::Info-->
																<!--begin::Post-->
																<span class="text-gray-800 fs-7 fw-normal pt-1"><?php echo $reply->comment_content; ?></span>
																<!--end::Post-->
															</div>
															<!--end::Info-->
														</div>
														<!--end::Reply-->
													</div>
													<?php
												}
												?>
												<!--end::Replies-->
												<!--begin::Separator-->
												<div class="separator mb-4"></div>
												<!--end::Separator-->
											</div>
											<!--end::Body-->
										</div>
									</div>
									<?php
								}
							}

						 	if ( $offering_status == 'pending' ) {

								/*
								 *
								 * Check contracts
								 *
								 */
								$isCallOff       = false;
								$allowToEditCost = true;
								$max_cost        = '';
								$value           = '';
								$SARSelected     = '';
								$USDSelected     = '';
								$contract_id     = get_post_meta( $post_id, '_company_' . get_current_user_id() . '_contract_id', true );
								if ( $contract_id && $contract_id != '' ) {
									$contractObj = get_post( $contract_id );
									if ( $contractObj && $contractObj->post_status == 'publish' ) {
										$contracts    = new Contracts();
										$isCallOff    = $contracts->check_contract_coverage( $contractObj->ID, $post_id );
										$contractData = $contracts->get_contract( $contractObj->ID );

										if ( 'yes' != get_post_meta( $contractObj->ID, '_edit_cost', true ) ) {
											$allowToEditCost = false;
										}

										$value    = $contracts->get_trip_cost( $contractObj->ID, $post_id );
										$max_cost = 'max="' . $contractData['remaining_money'] . '"';
										if ( 'SAR' == get_post_meta( $contract_id, '_currency', true ) ) {
											$SARSelected = 'selected = "selected"';
										} else {
											$USDSelected = 'selected = "selected"';
										}
									}
								}

								$comment_field = '
                                                       <div class="form-group d-flex mt-5  mb-10">
                                                            <label class="fw-bold fs-5 pt-3" style="width: 40%" for="validity_days">Offer validity days</label>
                                                            <div class="input-group">
                                                                <div class="input-group-prepend">
                                                                    <span class="input-group-text" style="border-top-right-radius: 0;  border-bottom-right-radius: 0;">Days</span>
                                                                </div>
                                                                <input type="number" class="form-control" step="1" name="validity_days" required/>
                                                            </div>
                                                        </div>
                                                        <div class="form-group d-flex mt-5  mb-10">
                                                            <label class="fw-bold fs-5 pt-3" style="width: 40%" for="transit_days">Transite days</label>
                                                            <div class="input-group">
                                                                <div class="input-group-prepend">
                                                                    <span class="input-group-text" style="border-top-right-radius: 0;  border-bottom-right-radius: 0;">Days</span>
                                                                </div>
                                                                <input type="number" class="form-control" step="1" name="transit_days" required/>
                                                            </div>
                                                        </div>
                                                        <div class="form-group d-flex mt-5  mb-10">
                                                            <label class="fw-bold fs-5 pt-3" style="width: 40%" for="port_free_days">PORT FREE DAYS</label>
                                                            <div class="input-group">
                                                                <div class="input-group-prepend">
                                                                    <span class="input-group-text" style="border-top-right-radius: 0;  border-bottom-right-radius: 0;">Days</span>
                                                                </div>
                                                                <input type="number" class="form-control" step="1" name="port_free_days" required/>
                                                            </div>
                                                        </div>
                                                        <div class="form-group d-flex mt-5  mb-10">
                                                            <label class="form-check form-switch form-switch-sm form-check-custom form-check-solid flex-stack mb-5">
                                                                <span class="form-check-label ms-0 fw-bolder fs-6 text-gray-700">Direct trip?</span>
                                                            </label>
                                                            <input class="form-check-input mr-2 ml-2" type="checkbox" id="direct_trip" name="direct_trip" value="direct_trip" checked="checked">
                                                        </div>
                                                        <div class="form-group">
                                                            <label for="comment" class="fw-bolder fs-4 mb-4">' . _x( 'Comment', 'wayz' ) . '</label>
                                                            <textarea id="comment" class="form-control" name="comment"></textarea>
                                                        </div>';

								if ( ! $allowToEditCost ) {
									$hiddenCost = ' hidden d-none';
								} else {
									$hiddenCost = '';
								}

								$cost_field = '
                                <div class="form-group d-flex mt-5  mb-10 ' . $hiddenCost . '">
                                    <label class="fw-bold fs-5 pt-3" style="width: 40%" for="offer_price">Enter Your Price</label>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <select class="form-control" name="offer_currency" class="input-group-text" style="border-top-right-radius: 0;  border-bottom-right-radius: 0;">
                                                <option value="USD" ' . $USDSelected . ' >USD</option>
                                                <option value="SAR" ' . $SARSelected . ' >SAR</option>
                                            </select>
                                        </div>
                                        <input type="number" class="form-control" placeholder="999.9" step="0.01" name="offer_price" value = "' . $value . '" ' . $max_cost . ' required/>
                                    </div>
                                </div>';

								$comment_field = $cost_field . $comment_field;

								if ( ! $isCallOff ) {
									$label_submit = 'Submit Quote';
								} else {
									$label_submit = 'Submit Call-Off';
									?>

									<h5 class="fw-bold mb-8">
										Client have active contract and
										<span class="d-inline-block position-relative ms-2">
											<span class="d-inline-block mb-2"><?php echo $contractData['remaining_text'] ?></span>
											<span class="d-inline-block position-absolute h-8px bottom-0 end-0 start-0 bg-success translate rounded"></span>
									</span>
									</h5>

									<?php
								}

								$comments_args = array(
									// Change the title of send button
									'label_submit'        => __( $label_submit, 'wayz' ),
									'submit_button'       => '
                                    <div class="text-center">
										<a href="#" id="declineLink" class="btn btn-danger me-3">Decline</a>
										<button name="%1$s" id="%2$s" type="submit" class="btn btn-primary">
										' . $label_submit . '
										</button>
										</div>',
									'title_reply'         => '',
									'comment_notes_after' => '',
									'comment_field'       => $comment_field,
								);
								comment_form( $comments_args );
							} else {
								?>
								<!--begin::Alert-->
								<div class="alert bg-success d-flex flex-column flex-sm-row w-100 p-5 mb-10">
									<!--begin::Icon-->
									<span class="svg-icon svg-icon-2hx svg-icon-light me-4 mb-5 mb-sm-0">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                            <path opacity="0.3" d="M21.4 8.35303L19.241 10.511L13.485 4.755L15.643 2.59595C16.0248 2.21423 16.5426 1.99988 17.0825 1.99988C17.6224 1.99988 18.1402 2.21423 18.522 2.59595L21.4 5.474C21.7817 5.85581 21.9962 6.37355 21.9962 6.91345C21.9962 7.45335 21.7817 7.97122 21.4 8.35303ZM3.68699 21.932L9.88699 19.865L4.13099 14.109L2.06399 20.309C1.98815 20.5354 1.97703 20.7787 2.03189 21.0111C2.08674 21.2436 2.2054 21.4561 2.37449 21.6248C2.54359 21.7934 2.75641 21.9115 2.989 21.9658C3.22158 22.0201 3.4647 22.0084 3.69099 21.932H3.68699Z" fill="currentColor"></path>
                                            <path d="M5.574 21.3L3.692 21.928C3.46591 22.0032 3.22334 22.0141 2.99144 21.9594C2.75954 21.9046 2.54744 21.7864 2.3789 21.6179C2.21036 21.4495 2.09202 21.2375 2.03711 21.0056C1.9822 20.7737 1.99289 20.5312 2.06799 20.3051L2.696 18.422L5.574 21.3ZM4.13499 14.105L9.891 19.861L19.245 10.507L13.489 4.75098L4.13499 14.105Z" fill="currentColor"></path>
                                        </svg>
                                    </span>
									<!--end::Icon-->
									<!--begin::Wrapper-->
									<div class="d-flex flex-column text-light pe-0 pe-sm-10">
										<!--begin::Title-->
										<h4 class="mb-2 light text-white">
											<?php
											if ( isset( $accepted_offer ) && $accepted_offer == $comment->comment_ID ) {
												$txt = 'Your offer has been accepted';
												$is_current_sp = true;
											} else {
												switch ( $offering_status ) {
													case 'rejected':
														$txt = 'You declined to offer';
														break;
													case 'cancelled':
														$txt = 'Unfortunately your offer is not selected';
														break;
													case 'closed' :
														$txt = 'Unfortunately, offering is closed.';
														break;
													default:
														$txt = 'You already made an offer <span>We will contact you soon.!</span>';
														break;
												}
											}
											echo $txt;
											?>
										</h4>
										<!--end::Title-->
									</div>
									<!--end::Wrapper-->
								</div>
								<!--end::Alert-->
								<?php
								if ( isset( $is_current_sp ) && $is_current_sp ) {
									?>
									<form action="#shipment_tracking" class="form mb-15 fv-plugins-bootstrap5 fv-plugins-framework" method="post">
										<?php
										$fields    = Utils::get_system_report_header();
										$sp_fields = Utils::get_shipment_data_fields();
										foreach ( $sp_fields as $fields_group ) {
											echo '<h1 class="fw-bold text-dark mb-9 mt-11">' . $fields_group['title'] . '</h1>';
											foreach ( $fields_group['fields'] as $key => $field ) {
												?>
												<!--begin::Input group-->
												<div class="d-flex flex-column mb-5 fv-row">
													<!--begin::Label-->
													<label class="fs-5 fw-semibold mb-2" for="<?php echo $key;?>"><?php echo $fields[ $key ]; ?></label>
													<!--end::Label-->
													<!--begin::Input-->
													<input class="form-control form-control-solid" type="<?php echo $field['type'];?>" name="<?php echo $key;?>" id="<?php echo $key;?>" value="<?php echo get_post_meta( $post_id, '_' . $key, true );?>">
													<!--end::Input-->
												</div>
												<!--end::Input group-->
												<?php
											}
										}
										?>
										<!--begin::Submit-->
										<input type="submit" class="btn btn-primary" value="Update shipment data" name="shipment_data">
									</form>
									<?php
								}
							}
							?>
							<!--end::Form-->
							<!--begin::Modal - New Address-->
						</div>
						<!--end::Card body-->
					</div>
					<!--end::Card-->
				</div>
				<!--end::Content-->
			</div>
			<!--end::Layout-->
		</div>
		<!--end::Container-->
	</div>
	<!--end::Post-->
<?php
endwhile;
get_footer();
?>