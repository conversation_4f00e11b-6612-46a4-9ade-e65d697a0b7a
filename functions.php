<?php

use <PERSON>z\Admin\Settings;
use <PERSON>z\BackOffice\Blocks_Data;
use Wayz\BackOffice\Customers;
use Wayz\Controllers\Offers;
use <PERSON>z\Controllers\ServiceProvider;
use Wayz\Controllers\User;
use Wayz\Notifications\Notification;
use Wayz\Permissions\Permissions;
use Wayz\PostTypes;
use Wayz\SystemActions;
use Wayz\TehemeSettings\ThemeSetup;

include_once( untrailingslashit( dirname(__FILE__ ) ) . '/vendor/autoload.php' );

/*
 * Setup the system
 */
new ThemeSetup();

function wayz_register_components() {
    PostTypes::init();
    ( new Settings() )->init();

    /*
     * Register companies user role
     */
    global $wp_roles;
	if ( ! isset( $wp_roles ) )
		$wp_roles = new WP_Roles();

	$subscriber = $wp_roles->get_role('subscriber');
	$wp_roles->add_role('shipping_company', 'Service Provider', $subscriber->capabilities );
	$wp_roles->add_role('sp_contact', 'Service Provider Contact', $subscriber->capabilities );
}
add_action('init', 'wayz_register_components');

add_action( 'comment_post', 'save_comment_meta_data' );
function save_comment_meta_data( $comment_id ) {
	if ( ( isset( $_POST['offer_price'] ) ) && ( $_POST['offer_price'] != '') ){
		$offer_price = floatval($_POST['offer_price']);
		add_comment_meta( $comment_id, 'offer_price', $offer_price );
    } elseif( isset( $_POST['transit_days'] ) ){
		add_comment_meta( $comment_id, 'offer_price', '0.0' );
    }

	if ( ( isset( $_POST['offer_currency'] ) ) && ( $_POST['offer_currency'] != '') ){
		add_comment_meta( $comment_id, 'offer_currency', $_POST['offer_currency'] );
	}

	if ( ( isset( $_POST['transit_days'] ) ) && ( $_POST['transit_days'] != '') ){
		$transit_days = intval($_POST['transit_days']);
		add_comment_meta( $comment_id, 'transit_days', $transit_days );
	}

	if ( ( isset( $_POST['validity_days'] ) ) && ( $_POST['validity_days'] != '' ) ) {
		add_comment_meta( $comment_id, 'validity_days', intval( $_POST['validity_days'] ) );
	}

	if ( ( isset( $_POST['port_free_days'] ) ) && ( $_POST['port_free_days'] != '' ) ) {
		add_comment_meta( $comment_id, 'port_free_days', intval( $_POST['port_free_days'] ) );
	}

	if ( ( isset( $_POST['direct_trip'] ) ) && ( $_POST['direct_trip'] != '' ) ) {
		add_comment_meta( $comment_id, 'direct_trip', 'yes' );
	} else {
		add_comment_meta( $comment_id, 'direct_trip', 'no' );
	}

	update_comment_meta( $comment_id, 'author_id', get_current_user_id() );
}

function wayz_register_meta_boxes() {
	add_meta_box( 'quotation_data', __( 'Quotation data :', 'wayz' ), 'ways_quotations_data', 'quotations' );
	add_meta_box( 'quotation_offers', __( 'Quotation offers :', 'wayz' ), 'ways_quotations_offers', 'quotations' );
}
add_action( 'add_meta_boxes', 'wayz_register_meta_boxes' );
function ways_quotations_data( $post ) {
	?>
	<table class="wp-list-table widefat fixed striped table-view-list pages">
		<thead>
		<tr>
			<th scope="col" id="author" class="manage-column">
				Field
			</th>
			<th scope="col" class="manage-column">
				Data
			</th>
		</thead>
		<tbody id="the-list">
        <tr id="post-2" class="iedit author-self level-0 post-2 type-page status-publish hentry">
			<?php
			$containers = get_post_meta(get_the_ID(),'_container-specification',true);
			$quantity = get_post_meta(get_the_ID(),'_quantity',true);
			?>
            <td>Containers</td>
            <td>
                <?php
                foreach ($containers as $key => $container){
                    echo $container. " x " .  $quantity[$key]."<br>";
	            }
                ?>
            </td>
        </tr>
		<?php
		$fields = array(
            'client_organization' => __('Client Company ID', 'wayz'),
			'_incoterms' => __('Incoterms', 'wayz'),
			'_pickup_address' => __('Pickup address ', 'wayz'),
			'_origin_country' => __(' Origin Country ', 'wayz'),
			'_origin_port' => __('Origin Port', 'wayz'),
			'_pick_up_date' => __('Pickup date', 'wayz'),
			'_destination_location' => __('Destination Location', 'wayz'),
			'_destination_country' => __('Destination Country', 'wayz'),
			'_destination_port' => __('Destination Port', 'wayz'),
			'_insurance' => __('Insurance', 'wayz'),
			'_customs-clearance' => __('Customs Clearance', 'wayz'),
			'_fright_companies' => __('Fright Forwarder Email', 'wayz'),
            '_note' => __('Note', 'wayz')
		);
		foreach ($fields as $key => $value){
			$result = get_post_meta(get_the_ID(),$key,true);
			if($result == '')
				continue;

            if($key == 'client_organization'){
                $client_organization_name = get_the_title($result);
                $result = $client_organization_name." (".$result.")";
            }
			?>
			<tr id="post-2" class="iedit author-self level-0 post-2 type-page status-publish hentry">
				<td><?php echo $value;?></td>
				<td><?php if(is_array($result)) echo implode(" || ",$result); else echo $result;?></td>
			</tr>
			<?php
		}
		?>
		</tbody>
	</table>
<?php
}

function ways_quotations_offers( $post ) {
	?>
    <table class="wp-list-table widefat fixed striped table-view-list pages">
        <thead>
        <tr>
            <th scope="col" id="author" class="manage-column">
                Company
            </th>
            <th scope="col" class="manage-column">
                Price
            </th>
            <th scope="col" class="manage-column">
	            TRANSIT TIME
            </th>
	        <th scope="col" class="manage-column">
		        PORT FREE DAYS
	        </th>
	        <th scope="col" class="manage-column">
		        Validity Days
	        </th>
            <th scope="col" class="manage-column">
                Comment
            </th>
        </thead>
        <tbody id="the-list">
        <?php
            $comments = get_comments( array(
                'post_id' => get_the_ID(),
                'parent'  => 0,
                'number'  => 9999
            ) );
            foreach ( $comments as $comment ) {
                ?>
                <tr>
                    <td><?php echo $comment->comment_author . '<br>' . get_comment_author_email($comment->comment_ID);?></td>
                    <td><?php echo get_comment_meta( $comment->comment_ID, 'offer_price', true ).' '.get_comment_meta( $comment->comment_ID, 'offer_currency', true ); ?></td>
                    <td><?php echo get_comment_meta( $comment->comment_ID, 'transit_days', true ); ?></td>
	                <td><?php echo get_comment_meta( $comment->comment_ID, 'port_free_days', true ); ?></td>
	                <td><?php echo get_comment_meta( $comment->comment_ID, 'validity_days', true ); ?></td>
	                <td><?php echo $comment->comment_content; ?></td>
                </tr>
	            <?php
            }
        ?>
        </tbody>
    </table>
	<?php
}

/*
 * Get page link by template
 */
function get_page_url_by_template($TEMPLATE_NAME){
	$url = null;
	$pages = get_pages(array(
		'meta_key' => '_wp_page_template',
		'meta_value' => $TEMPLATE_NAME
	));
	if(isset($pages[0])) {
		$url = get_page_link($pages[0]->ID);
	}
	return $url;
}

/*
 * Wrong login issue
 */
add_action( 'wp_login_failed', 'wayz_login_fail' );  // hook failed login
function wayz_login_fail( $username ) {
	$referrer = $_SERVER['HTTP_REFERER'];
	if ( !empty($referrer) ) {
		wp_redirect( $referrer . '?login=failed&username='.$username );
		exit;
	}
}

/*
 * Order and Shipment status notification
 */
add_action( 'woocommerce_order_status_processing', function ($order_id) {
	( new Notification() )->new(get_post_meta($order_id,'_customer_user',true),'👌 New order created successfully: #'.$order_id,'order_status',$order_id);
} );

add_action( 'woocommerce_order_status_completed', function ($order_id) {
	( new Notification() )->new(get_post_meta($order_id,'_customer_user',true),'👍 Your order #'.$order_id.' completed.','order_status',$order_id);
} );

add_action( 'woocommerce_order_status_cancelled', function ($order_id) {
	( new Notification() )->new(get_post_meta($order_id,'_customer_user',true),'😔 Your order #'.$order_id.' canceled.','order_status',$order_id);
} );

add_action('updated_post_meta', function ($meta_id, $post_id, $meta_key, $meta_value){
	if($meta_key == '_shipment_status' ){
        switch ($meta_value){
	        case 'to-origin-port':
		        ( new Notification() )->new(get_post_meta($post_id,'_customer_user',true),'🚢 Your shipment #'.$post_id.' on the way to origin port.','shipment_status',$post_id);
		        break;

	        case 'at-origin-port':
		        ( new Notification() )->new(get_post_meta($post_id,'_customer_user',true),'🚢 Your shipment #'.$post_id.' arrived at origin port.','shipment_status',$post_id);
		        break;

	        case 'in-transit':
		        ( new Notification() )->new(get_post_meta($post_id,'_customer_user',true),'🚢 Your shipment #'.$post_id.' in transit.','shipment_status',$post_id);
		        break;

	        case 'at-destination-port':
		        ( new Notification() )->new(get_post_meta($post_id,'_customer_user',true),'🚢 Your shipment #'.$post_id.' arrived at destination port.','shipment_status',$post_id);
		        break;

            default;
        }
	}
}, 10, 4);
/*
 * AJAX
 */
add_action( 'wp_enqueue_scripts', 'wayz_js_enqueue' );
function wayz_js_enqueue($hook) {
	wp_enqueue_script( 'wayz-functions', get_template_directory_uri().'/assets/js/functions.js' , array('jquery'),'1.3',true );
	wp_localize_script( 'wayz-functions', 'wayz_object',
		array(
                'ajax_url' => admin_url( 'admin-ajax.php' )
        )
    );
}

add_action( 'wp_ajax_refresh_user_addresses', 'refresh_user_addresses' );
add_action( 'wp_ajax_nopriv_refresh_user_addresses', 'refresh_user_addresses' );
function refresh_user_addresses() {
    $options = "<option></option>";
	$addresses = ( new \Wayz\Controllers\Addresses )->list( get_current_user_id() );
	foreach (array_reverse($addresses) as $key => $address){
		if($key == 0)
			$selected = 'selected="selected"';
		else
			$selected = "";

		$address_text = $address['address1'].','.$address['city'].','.$address['country'];
		$options = $options.'<option '. $selected .' value="'.$address_text.'">'.$address_text.'</option>';
	}
    echo $options;
	wp_die();
}

add_action( 'wp_ajax_refresh_user_companies', 'refresh_user_companies' );
add_action( 'wp_ajax_nopriv_refresh_user_companies', 'refresh_user_companies' );
function refresh_user_companies() {
	$option    = [];
	$companies = (array) get_user_meta( get_current_user_id(), 'fright_company' );

	foreach ( array_reverse( $companies ) as $key => $company_data ) {

		if ( is_array( $company_data ) ) {
			$company_profile = get_user_by( 'ID', $company_data['id'] );
			$company_name    = $company_data['name'];
		} else {
			$company_profile = get_user_by( 'ID', $company_data );
			$company_name    = $company_profile->display_name;
		}

		if ( ! $company_profile ) {
			break;
		}

		$option = [
			'email' => $company_profile->user_email,
			'name'  => $company_name
		];

		break;
	}
	wp_send_json_success( $option );
	wp_die();
}

/*
 * Get country cities and ports
 */
add_action( 'wp_ajax_wayz_get_country_cites', 'wayz_get_country_cites' );
add_action( 'wp_ajax_nopriv_wayz_get_country_cites', 'wayz_get_country_cites' );
function wayz_get_country_cites() {
    $cities = '';
	if(isset($_POST['country'])){
		global $wpdb;
		$sql = "SELECT * FROM wayz_ports WHERE country = %s";
		$sql = $wpdb->prepare($sql,$_POST['country']);
		$result = $wpdb->get_results($sql,ARRAY_A);
        foreach ($result as $city){
            $cities .= '<option value="'.$city['city'].'">'.$city['city'].'</option>';
        }
	}

    echo $cities;
}

add_action( 'wp_ajax_wayz_get_country_ports', 'wayz_get_country_ports' );
add_action( 'wp_ajax_nopriv_wayz_get_country_ports', 'wayz_get_country_ports' );
function wayz_get_country_ports() {
	$ports = '';
	if ( isset( $_POST['country'] ) ) {
		global $wpdb;
		$sql    = "SELECT * FROM wayz_ports WHERE country = %s And port_type = %s";
		$sql    = $wpdb->prepare( $sql, wc_clean( $_POST['country'] ), wc_clean( $_POST['port_type'] ?? 'sea' ) );
		$result = $wpdb->get_results( $sql, ARRAY_A );
		foreach ( $result as $port ) {
			$ports .= '<option value="' . $port['port'] . '">' . $port['port'] . '</option>';
		}
	}

	echo $ports;
}

function wayz_get_countries() {
	global $wpdb;
	$sql = "SELECT DISTINCT country FROM wayz_ports";
	return $wpdb->get_results( $sql, ARRAY_A );
}

// Allow empty comment
add_filter( 'allow_empty_comment', '__return_true' );
add_filter('duplicate_comment_id', '__return_false');

/*
 * Order / Shipment Tracking
 */
function wayz_order_tracking(WC_Order $order){
	$_shipment_status = get_post_meta( $order->get_id(), '_shipment_status', true );
	woocommerce_wp_select( array(
		'id'      => '_shipment_status',
		'label'   => __( 'Shipment status', 'wayz' ),
		'options' =>  array(
			'origin-warehouse' => __( 'Origin warehouse', 'wayz' ),
			'to-origin-port' => __( 'On the way to origin port', 'wayz' ),
			'at-origin-port' => __( 'Arrived at origin port', 'wayz' ),
			'in-transit' => __( 'In transit', 'wayz' ),
			'at-destination-port' => __( 'Arrived at destination port', 'wayz' )
		),
		'value'   => $_shipment_status,
	) );
}
add_action( 'woocommerce_admin_order_data_after_order_details', 'wayz_order_tracking' );

function wayz_save_custom_fields_values( $post_id ) {
	if ( ! current_user_can( 'edit_post', $post_id ) ) {
		return ;
	}

	/*
	 * Shipment Status
	 */
	if ( isset($_POST['_shipment_status']) ) {
		update_post_meta($post_id,'_shipment_status',$_POST['_shipment_status']);
	}
}
add_action( 'save_post', 'wayz_save_custom_fields_values', 1, 1 );
/*
 * Redirect to custom login after logout
 */
add_action('wp_logout','auto_redirect_after_logout');
function auto_redirect_after_logout(){
	wp_safe_redirect( home_url() );
	exit;
}

/*
 * Uploader shortcode
 */
add_shortcode( 'wayz_uploader', 'wayz_uploader_form' );
function wayz_uploader_form( $atts ) {
    $current_url="//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
    ?>
    <!--begin::Form-->
    <form id="uploadFilesForm" class="form" method="post" action="<?php echo $current_url;?>"  enctype="multipart/form-data">
        <!--begin::Modal header-->
        <div class="modal-header">
            <!--begin::Modal title-->
            <h2 class="fw-bolder">Upload files</h2>
            <!--end::Modal title-->
            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-icon-primary" data-bs-dismiss="modal">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                <span class="svg-icon svg-icon-1">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor" />
                        <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor" />
                    </svg>
                </span>
                <!--end::Svg Icon-->
            </div>
            <!--end::Close-->
        </div>
        <!--end::Modal header-->
        <!--begin::Modal body-->
        <div class="modal-body pt-10 pb-15 px-lg-17">
            <!--begin::Input group-->
            <div class="form-group text-center">
                <input type="file" class="form-control form-control-lg form-control-solid" name="request_file" />
                <input hidden="hidden" type="input" value="<?php echo $atts[ 'request_id' ];?>" name="request_id" />
                <input id="uploadFileBtn" type="submit" name="submit" value="Upload" class="btn btn-primary mt-3"/>
            </div>
            <!--end::Input group-->
        </div>
        <!--end::Modal body-->
    </form>
    <!--end::Form-->
    <?php
}

/*
 * Assets
 */
new \Wayz\Front_End\Assets();

/*
 * Back Office
 */
new User();
new Customers();
new Blocks_Data();
new ServiceProvider();
new Permissions();
new Offers();
new SystemActions();
new Notification();